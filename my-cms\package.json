{"name": "my-cms", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi", "deploy": "strapi deploy"}, "dependencies": {"@strapi/plugin-cloud": "4.25.23", "@strapi/plugin-i18n": "4.25.23", "@strapi/plugin-users-permissions": "4.25.23", "@strapi/strapi": "^5.19.0", "better-sqlite3": "8.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "5.3.4", "styled-components": "5.3.3"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "2124f202-9384-44a2-9bc9-20c4fbd1131c"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}