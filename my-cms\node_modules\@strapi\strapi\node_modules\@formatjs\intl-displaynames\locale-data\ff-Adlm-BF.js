/* @generated */
// prettier-ignore
if (Intl.DisplayNames && typeof Intl.DisplayNames.__addLocaleData === 'function') {
  Intl.DisplayNames.__addLocaleData({
  "data": {
    "patterns": {
      "locale": "{0} ({1})"
    },
    "types": {
      "calendar": {
        "long": {
          "buddhist": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤄𞤵𞥅𞤣𞤢𞤴𞤢𞤲𞤳𞤮",
          "chinese": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤅𞤭𞥅𞤲𞤭𞤲𞤳𞤮",
          "coptic": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤑𞤮𞤨𞤼𞤭𞤲𞤳𞤮",
          "dangi": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤁𞤢𞤲𞤺𞤭𞤲𞤳𞤮",
          "ethiopic": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤀𞤦𞤢𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "ethiopic-amete-alem": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤀𞤥𞤼𞤫 𞤀𞤤𞤫𞥅𞤥 𞤀𞤦𞤢𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "gregorian": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤘𞤫𞤪𞤺𞤮𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "hebrew": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤋𞤦𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "indian": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤘𞤫𞤲𞤣𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤋𞤲𞤣𞤭𞤴𞤢",
          "islamic": "𞤙𞤢𞤤𞤥𞤫𞤪𞤫 𞤂𞤭𞤧𞤤𞤢𞥄𞤥𞤴𞤢𞤲𞤳𞤮",
          "islamic-civil": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤂𞤭𞤧𞤤𞤢𞥄𞤥𞤴𞤢𞤲𞤳𞤮 (𞤢𞤤𞥆𞤵𞤱𞤢𞤤, 𞤬𞤫𞤱𞤲𞥋𞤣𞤮 𞤲𞥋𞤦𞤫𞤯𞥆𞤢𞥄𞤳𞤵)",
          "islamic-rgsa": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤂𞤭𞤧𞤤𞤢𞥄𞤥𞤵 (𞤼𞤭𞤲𞤢𞤲𞤣𞤫 𞤀𞥄𞤪𞤢𞤦𞤭 𞤅𞤢𞤱𞤮𞤣𞤭𞥅(",
          "islamic-tbla": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤂𞤭𞤧𞤤𞤢𞥄𞤥𞤵 (𞤀𞤤𞥆𞤵𞤲𞤳𞤮, 𞤊𞤫𞤱𞤣𞤮 𞤋𞤲𞤳𞤮𞥅𞤣𞤭𞤲𞤳𞤮)",
          "islamic-umalqura": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤂𞤭𞤧𞤤𞤢𞥄𞤥𞤵 (𞤓𞤥𞥆𞤵𞤤-𞤗𞤵𞤪𞤢𞥄)",
          "iso8601": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 ISO-8601",
          "japanese": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤔𞤢𞥄𞤨𞤮𞤲𞤭𞤲𞤳𞤮",
          "persian": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤊𞤢𞥄𞤪𞤭𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "roc": "𞤙𞤢𞤤𞤯𞤭𞤥𞤫𞤪𞤫 𞤘𞤫𞤲𞤣𞤭𞤴𞤢𞤲𞤳𞤮 𞤅𞤭𞥅𞤲"
        },
        "narrow": {
        },
        "short": {
        }
      },
      "currency": {
        "long": {
          "AED": "𞤁𞤭𞤪𞤸𞤢𞤥𞤵 𞤋𞤥𞤢𞥄𞤪𞤢𞤼𞤭𞤲𞤳𞤮",
          "AFA": "𞤀𞤬𞤺𞤢𞥄𞤲 𞤀𞤬𞤺𞤢𞥄𞤲𞤭 (𞥑𞥙𞥒𞥗-𞥒𞥐𞥐𞥒)",
          "AFN": "𞤀𞤬𞤿𞤢𞤲𞤭 𞤀𞤬𞤿𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "ALL": "𞤂𞤫𞤳 𞤀𞤤𞤦𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "AMD": "𞤁𞤢𞤪𞤢𞤥𞤵 𞤀𞤪𞤥𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "ANG": "𞤊𞤵𞤤𞤮𞤪𞤭𞤲 𞤀𞤲𞤼𞤭𞤴𞤢𞤲𞤳𞤮",
          "AOA": "𞤑𞤵𞤱𞤢𞤲𞥁𞤢 𞤀𞤲𞤺𞤮𞤤𞤢𞤲𞤳𞤮",
          "ARA": "𞤌𞤧𞤼𞤪𞤢𞤤 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞤲𞤳𞤮",
          "ARL": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤂𞤫𞤴 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞤲𞤳𞤮 (𞥑𞥙𞥗𞥐-𞥑𞥙𞥘𞥓)",
          "ARM": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞤲𞤳𞤮 (𞥑𞥘𞥘𞥑-𞥑𞥙𞥗𞥐)",
          "ARP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞤲𞤳𞤮 (𞥑𞥙𞥘𞥓-𞥑𞥙𞥘𞥕)",
          "ARS": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞤲𞤳𞤮",
          "AUD": "𞤁𞤢𞤤𞤢 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞤲𞤳𞤮",
          "AWG": "𞤊𞤵𞤤𞤮𞤪𞤭𞤲 𞤀𞤪𞤵𞤦𞤢𞤲𞤳𞤮",
          "AZN": "𞤃𞤢𞤲𞤢𞥄𞤼𞤵 𞤀𞥁𞤫𞤪𞤦𞤢𞤴𞤶𞤢𞤲𞤳𞤮",
          "BAM": "𞤃𞤢𞤪𞤳 𞤄𞤮𞤧𞤲𞤭𞤴𞤢-𞤖𞤫𞤪𞤶𞤫𞤺𞤮𞤾𞤭𞤲𞤳𞤮 𞤱𞤢𞤴𞤤𞤮𞤼𞤮𞥅𞤯𞤭",
          "BBD": "𞤁𞤢𞤤𞤢 𞤄𞤢𞤪𞤦𞤢𞤣𞤭𞤴𞤢𞤲𞤳𞤮",
          "BDT": "𞤚𞤢𞤪𞤢 𞤄𞤢𞤲𞤺𞤭𞤤𞤢𞤣𞤫𞥅𞤧𞤭𞤲𞤳𞤮",
          "BGN": "𞤂𞤫𞥅𞤾 𞤄𞤭𞤤𞤺𞤢𞤪𞤭𞤲𞤳𞤮",
          "BHD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤄𞤢𞤸𞤢𞤪𞤢𞥄𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "BIF": "𞤊𞤢𞤪𞤢𞤲 𞤄𞤵𞤪𞤵𞤲𞤣𞤭𞤲𞤳𞤮",
          "BMD": "𞤁𞤢𞤤𞤢 𞤄𞤵𞤪𞤥𞤵𞤣𞤢𞤲𞤳𞤮",
          "BND": "𞤁𞤢𞤤𞤢 𞤄𞤵𞤪𞤲𞤫𞤴𞤢𞤲𞤳𞤮",
          "BOB": "𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤮 𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤳𞤮",
          "BOL": "𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤮 𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤳𞤮 (𞥑𞥘𞥖𞥓-𞥑𞥙𞥖𞥓)",
          "BOP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤳𞤮",
          "BOV": "𞤃𞤾𞤣𞤮𞤤 𞤄𞤮𞤤𞤭𞤾𞤭𞤴𞤢𞤲𞤳𞤮",
          "BRB": "𞤑𞤫𞤪𞤮𞤧𞤫𞤪𞤮 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 𞤑𞤫𞤧𞤮 (𞥑𞥙𞥖𞥗-𞥑𞥙𞥘𞥖)",
          "BRC": "𞤑𞤵𞤪𞥁𞤢𞤣𞤮𞥅 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥘𞥖-𞥑𞥙𞥘𞥙)",
          "BRE": "𞤑𞤵𞤪𞥁𞤫𞤴𞤪𞤮 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥙𞥐-𞥑𞥙𞥙𞥓)",
          "BRL": "𞤈𞤭𞤴𞤢𞤤 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮",
          "BRN": "𞤑𞤵𞤪𞥁𞤢𞤣𞤮𞥅 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥘𞥙-𞥑𞥙𞥙𞥐)",
          "BRR": "𞤑𞤵𞤪𞥁𞤫𞤴𞤪𞤮 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥙𞥓-𞥑𞥙𞥙𞥔)",
          "BRZ": "𞤑𞤵𞤪𞥁𞤫𞤴𞤪𞤮 𞤄𞤪𞤢𞤧𞤭𞤤𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥔𞥒-𞥑𞥙𞥖𞥗)",
          "BSD": "𞤁𞤢𞤤𞤢 𞤄𞤢𞤸𞤢𞤥𞤭𞤴𞤢𞤲𞤳𞤮",
          "BTN": "𞤐𞤘𞤵𞤤𞤼𞤵𞤪𞤵𞤥𞤵 𞤄𞤵𞤼𞤢𞤲𞤭𞤲𞤳𞤮",
          "BWP": "𞤆𞤵𞤤𞤢 𞤄𞤮𞤼𞤵𞤧𞤱𞤢𞤲𞤢𞤲𞤳𞤮",
          "BYN": "𞤈𞤵𞥅𞤦𞤮𞤤 𞤄𞤫𞤤𞤢𞤪𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "BZD": "𞤁𞤢𞤤𞤢 𞤄𞤫𞤤𞤭𞥅𞤧𞤴𞤢𞤲𞤳𞤮",
          "CAD": "𞤁𞤢𞤤𞤢 𞤑𞤢𞤲𞤢𞤣𞤭𞤴𞤢𞤲𞤳𞤮",
          "CDF": "𞤊𞤢𞤪𞤢𞤲 𞤑𞤮𞤲𞤺𞤮𞤲𞤳𞤮",
          "CHF": "𞤊𞤢𞤪𞤢𞤲 𞤅𞤵𞤱𞤭𞥅𞤧",
          "CLE": "𞤉𞤧𞤳𞤵𞤣𞤮𞥅 𞤕𞤭𞤤𞤫𞥊𞤴𞤢𞤲𞤳𞤮",
          "CLF": "𞤅𞤢𞤤𞤲𞤣𞤵 𞤂𞤭𞤥𞤮𞥅𞤪𞤫 𞤕𞤭𞤤𞤫𞥊𞤴𞤢𞤲𞤳𞤮",
          "CLP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤕𞤭𞤤𞤫𞥊𞤴𞤢𞤲𞤳𞤮",
          "CNH": "𞤒𞤵𞤱𞤢𞤲 𞤕𞤢𞤴𞤲𞤭𞤲𞤳𞤮 (𞤺𞤢𞥄𞤲𞤭𞤲𞤳𞤮)",
          "CNY": "𞤒𞤵𞤱𞤢𞥄𞤲 𞤕𞤢𞤴𞤲𞤭𞤲𞤳𞤮",
          "COP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤑𞤮𞤤𞤮𞤥𞤦𞤭𞤴𞤢𞤲𞤳𞤮",
          "COU": "𞤅𞤢𞤤𞤲𞤣𞤵 𞤔𞤢𞤪𞤮 𞤳𞤮𞤤𞤮𞤥𞤦𞤭𞤴𞤢𞤲𞤳𞤮",
          "CRC": "𞤑𞤮𞤤𞤮𞥅𞤲 𞤑𞤮𞤧𞤼𞤢𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "CUC": "𞤆𞤫𞤧𞤮 𞤑𞤵𞤦𞤢𞤲𞤳𞤮 𞤏𞤢𞤴𞤤𞤮𞤼𞤮𞥅𞤲𞥋𞤺𞤮",
          "CUP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤑𞤵𞤦𞤢𞤲𞤳𞤮",
          "CVE": "𞤉𞤧𞤳𞤵𞤣𞤮 𞤑𞤢𞤨-𞤜𞤫𞥅𞤪𞤣𞤢𞤲𞤳𞤮",
          "CZK": "𞤑𞤮𞤪𞤵𞤲𞤢 𞤕𞤫𞥅𞤳𞤭𞤲𞤳𞤮",
          "DJF": "𞤊𞤢𞤪𞤢𞤲 𞤔𞤭𞤦𞤵𞤼𞤭𞤲𞤳𞤮",
          "DKK": "𞤑𞤮𞤪𞤲𞤫 𞤁𞤢𞤲𞤭𞥅𞤧𞤭𞤲𞤳𞤮",
          "DOP": "𞤆𞤫𞤧𞤮 𞤁𞤮𞤥𞤭𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "DZD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤀𞤤𞤶𞤢𞤪𞤭𞤲𞤳𞤮",
          "ECS": "𞤅𞤵𞥅𞤳𞤵𞤪𞤫𞥊𞥅 𞤉𞤳𞤵𞤱𞤢𞤣𞤮𞥅𞤪𞤴𞤢𞤲𞤳𞤮",
          "ECV": "𞤅𞤢𞤤𞤲𞤣𞤵 𞤔𞤮𞤪𞤮 𞤉𞤳𞤵𞤱𞤢𞤣𞤮𞥅𞤪𞤴𞤢𞤲𞤳𞤮 𞤚𞤢𞤦𞤭𞤼𞤵𞤲𞥋𞤺𞤮",
          "EGP": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤃𞤭𞤧𞤭𞤪𞤢𞤲𞤳𞤮",
          "ERN": "𞤐𞤢𞤳𞤬𞤢 𞤉𞤪𞤭𞤼𞤫𞤪𞤭𞤲𞤳𞤮",
          "ESP": "",
          "ETB": "𞤄𞤭𞤪 𞤖𞤢𞤦𞤢𞤧𞤭𞤲𞤳𞤮",
          "EUR": "𞤒𞤵𞤪𞤮𞥅",
          "FJD": "𞤁𞤢𞤤𞤢 𞤊𞤭𞤶𞤭𞤴𞤢𞤲𞤳𞤮",
          "FKP": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤅𞤵𞤪𞤭𞥅𞤶𞤫 𞤊𞤢𞤤𞤳𞤵𞤤𞤢𞤲𞤣𞤭𞤳𞤮",
          "GBP": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤄𞤪𞤭𞤼𞤭𞥅𞤧𞤭𞤲𞤳𞤮",
          "GEL": "𞤂𞤢𞥄𞤪𞤭 𞤔𞤮𞤪𞤶𞤭𞤴𞤢𞤲𞤳𞤮",
          "GHS": "𞤅𞤭𞤣𞤭 𞤘𞤢𞤲𞤢𞤲𞤳𞤮",
          "GIP": "𞤆𞤢𞤱𞤲𞥋𞤣𞤵 𞤔𞤭𞤤𞤦𞤪𞤢𞤤𞤼𞤢𞤪",
          "GMD": "𞤁𞤢𞤤𞤢𞤧𞤭 𞤘𞤢𞤥𞤦𞤭𞤲𞤳𞤮",
          "GNF": "𞤊𞤢𞤪𞤢𞤲 𞤘𞤭𞤲𞤫𞤲𞤳𞤮",
          "GTQ": "𞤑𞤫𞤼𞤵𞥁𞤢𞤤 𞤘𞤵𞤱𞤢𞤼𞤫𞤥𞤢𞤤𞤢𞤲𞤳𞤮",
          "GYD": "𞤁𞤢𞤤𞤢 𞤘𞤵𞤴𞤢𞤲𞤫𞥅𞤧𞤭𞤲𞤳𞤮",
          "HKD": "𞤁𞤢𞤤𞤢 𞤖𞤮𞤲𞤳𞤮𞤲",
          "HNL": "𞤂𞤫𞤥𞤨𞤭𞤪𞤢 𞤖𞤮𞤲𞤣𞤵𞤪𞤢𞤲𞤳𞤮",
          "HRK": "𞤑𞤵𞤲𞤢 𞤑𞤵𞤪𞤢𞥄𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "HTG": "𞤘𞤵𞥅𞤪𞤣𞤫 𞤖𞤢𞤴𞤼𞤭𞤴𞤢𞤲𞤳𞤮",
          "HUF": "𞤊𞤮𞤪𞤭𞤲𞤼𞤵 𞤖𞤵𞤲𞤺𞤢𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "IDR": "𞤈𞤵𞤨𞤭𞤴𞤢 𞤋𞤲𞤣𞤮𞤲𞤫𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "ILS": "𞤡𞤫𞤳𞤫𞤤 𞤋𞤧𞤪𞤢𞥄𞤤𞤭𞤴𞤢𞤲𞤳𞤮",
          "INR": "𞤈𞤵𞥅𞤨𞤭𞥅 𞤖𞤭𞤲𞤣𞤭𞤧𞤼𞤢𞤲𞤳𞤮",
          "IQD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤋𞤪𞤢𞥄𞤳𞤭𞤴𞤢𞤲𞤳𞤮",
          "IRR": "𞤈𞤭𞤴𞤢𞥄𞤤 𞤋𞤪𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "ISK": "𞤑𞤮𞤪𞤮𞤲𞤢 𞤀𞤴𞤧𞤭𞤤𞤢𞤲𞤣𞤭𞤲𞤳𞤮",
          "JMD": "𞤁𞤢𞤤𞤢 𞤔𞤢𞤥𞤢𞤴𞤭𞤲𞤳𞤮",
          "JOD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤔𞤮𞤪𞤣𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "JPY": "𞤒𞤫𞤲 𞤔𞤢𞤨𞤢𞤲𞤳𞤮",
          "KES": "𞤅𞤭𞤤𞤭𞤲 𞤑𞤫𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "KGS": "𞤅𞤮𞤥𞤵 𞤑𞤭𞤪𞤺𞤭𞤧𞤼𞤢𞤲𞤭𞤲𞤳𞤮",
          "KHR": "𞤈𞤭𞤴𞤢𞤤 𞤑𞤢𞤥𞤦𞤮𞤣𞤭𞤴𞤢𞤲𞤳𞤮",
          "KMF": "𞤊𞤢𞤪𞤢𞤲 𞤑𞤮𞤥𞤮𞤪𞤭𞤲𞤳𞤮",
          "KPW": "𞤏𞤮𞤲 𞤁𞤮𞤱𞤣𞤮𞤱𞤪𞤭 𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞤲𞤳𞤮",
          "KRW": "𞤱𞤮𞤲 𞤂𞤫𞤴𞤤𞤫𞤴𞤪𞤭 𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞤲𞤳𞤮",
          "KWD": "𞤁𞤋𞤲𞤢𞥄𞤪 𞤑𞤵𞤱𞤢𞤴𞤼𞤭𞤴𞤢𞤲𞤳𞤮",
          "KYD": "𞤁𞤢𞤤𞤢 𞤅𞤵𞤪𞤭𞥅𞤶𞤫 𞤑𞤢𞤴𞤥𞤢𞥄𞤲",
          "KZT": "𞤚𞤫𞤲𞤺𞤫 𞤑𞤢𞥁𞤢𞤳𞤭𞤧𞤼𞤢𞤲𞤭𞤲𞤳𞤮",
          "LAK": "𞤑𞤭𞤨𞤵 𞤂𞤢𞤱𞤮𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "LBP": "𞤆𞤢𞤱𞤲𞥋𞤣𞤵 𞤂𞤭𞤦𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "LKR": "𞤈𞤵𞥅𞤨𞤭𞥅 𞤅𞤭𞤪𞤭-𞤂𞤢𞤲𞤳𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "LRD": "𞤁𞤢𞤤𞤢 𞤂𞤭𞤦𞤫𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "LTL": "",
          "LVL": "",
          "LYD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤂𞤭𞤦𞤭𞤴𞤢𞤲𞤳𞤮",
          "MAD": "𞤁𞤭𞤪𞤸𞤢𞤥𞤵 𞤃𞤮𞤪𞤮𞤳𞤢𞤲𞤳𞤮",
          "MDL": "𞤂𞤭𞥅𞤱𞤮 𞤃𞤮𞤤𞤣𞤮𞤾𞤢𞤲𞤳𞤮",
          "MGA": "𞤀𞤪𞤭𞤴𞤢𞤪𞤭 𞤃𞤢𞤤𞤺𞤢𞤲𞤭𞤲𞤳𞤮",
          "MKD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤃𞤢𞤧𞤫𞤣𞤮𞤲𞤭𞤲𞤳𞤮",
          "MMK": "𞤑𞤭𞤴𞤢𞤼𞤵 𞤃𞤭𞤴𞤢𞤥𞤢𞤪𞤭𞤲𞤳𞤮",
          "MNT": "𞤚𞤵𞤺𞤵𞤪𞤭𞤳𞤵 𞤃𞤮𞤲𞤺𞤮𞤤𞤭𞤴𞤢𞤲𞤳𞤮",
          "MOP": "𞤆𞤢𞤼𞤢𞤳𞤢 𞤃𞤢𞤳𞤢𞤱𞤮𞤴𞤢𞤲𞤳𞤮",
          "MRO": "𞤓𞤺𞤭𞤴𞤢 𞤃𞤮𞤪𞤭𞤼𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥗𞥓 - 𞥒𞥐𞥑𞥗)",
          "MRU": "𞤓𞤺𞤭𞤴𞤢 𞤃𞤮𞤪𞤭𞤼𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "MUR": "𞤈𞤵𞤨𞤭𞥅 𞤃𞤮𞤪𞤭𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "MVP": "𞤈𞤵𞥅𞤨𞤭𞥅 𞤃𞤢𞤤𞤣𞤭𞥅𞤬 (𞥑𞥙𞥔𞥗-𞥑𞥙𞥘𞥑)",
          "MVR": "𞤈𞤵𞤬𞤭𞤴𞤢𞥄 𞤃𞤢𞤤𞤣𞤭𞤾𞤭𞤴𞤢𞤲𞤳𞤮",
          "MWK": "𞤑𞤢𞤱𞤢𞤷𞤢 𞤃𞤢𞤤𞤢𞤱𞤭𞤲𞤳𞤮",
          "MXN": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞤴𞤢𞤲𞤳𞤮",
          "MXP": "𞤑𞤢𞥄𞤤𞤭𞤧𞤫 𞤆𞤫𞥅𞤧𞤮𞥅 𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞤴𞤢𞤲𞤳𞤮 (𞥑𞥘𞥖𞥑-𞥑𞥙𞥙𞥒)",
          "MXV": "𞤅𞤢𞤤𞤲𞤣𞤵 𞤊𞤭𞤤𞤮 𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤵",
          "MYR": "𞤈𞤭𞤲𞤺𞤵𞤼𞤵 𞤃𞤢𞤤𞤫𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮",
          "MZN": "𞤃𞤫𞤼𞤭𞤳𞤮𞤤 𞤃𞤮𞥁𞤢𞤥𞤦𞤭𞤲𞤳𞤮",
          "NAD": "𞤁𞤢𞤤𞤢 𞤐𞤢𞤥𞤭𞤥𞤦𞤭𞤲𞤳𞤮",
          "NGN": "𞤐𞤢𞤴𞤪𞤢 𞤐𞤢𞤶𞤭𞤪𞤢𞤴𞤢𞤲𞤳𞤮",
          "NIC": "𞤑𞤮𞥅𞤪𞤣𞤮𞤦𞤢 𞤐𞤭𞤳𞤢𞤪𞤢𞤺𞤵𞤱𞤢𞤲𞤳𞤮 (𞥑𞥙𞥘𞥘-𞥑𞥙𞥙𞥑)",
          "NIO": "𞤑𞤮𞥅𞤪𞤣𞤮𞤦𞤢 𞤐𞤭𞤳𞤢𞤪𞤢𞤺𞤵𞤱𞤢𞤲𞤳𞤮",
          "NOK": "𞤑𞤪𞤮𞤲𞤫 𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤲𞤳𞤮",
          "NPR": "𞤈𞤵𞥅𞤨𞤭𞥅 𞤐𞤫𞤨𞤢𞤤𞤭𞤴𞤢𞤲𞤳𞤮",
          "NZD": "𞤁𞤢𞤤𞤢 𞤐𞤫𞤱 𞤟𞤫𞤤𞤢𞤲𞤣",
          "OMR": "𞤈𞤭𞤴𞤢𞥄𞤤 𞤌𞤥𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "PAB": "𞤄𞤢𞤤𞤦𞤮𞤱𞤢 𞤆𞤢𞤲𞤢𞤥𞤢𞤴𞤢𞤲𞤳𞤮",
          "PEI": "𞤋𞤲𞤼𞤭 𞤨𞤫𞤪𞤵𞤴𞤢𞤲𞤳𞤮",
          "PEN": "𞤅𞤮𞤤 𞤆𞤫𞤪𞤵𞤲𞤳𞤮",
          "PES": "𞤅𞤮𞤤 𞤆𞤫𞤪𞤵𞤴𞤢𞤲𞤳𞤮 (𞥑𞥘𞥖𞥓-𞥑𞥙𞥖𞥕)",
          "PGK": "𞤑𞤭𞤲𞤢 𞤆𞤢𞤨𞤵𞤱𞤢 𞤐𞤫𞤱-𞤘𞤭𞤲𞤫𞤲𞤳𞤮",
          "PHP": "𞤆𞤭𞤧𞤮 𞤊𞤭𞤤𞤭𞤨𞥆𞤭𞤲𞤳𞤮",
          "PKR": "𞤈𞤵𞥅𞤨𞤭𞥅 𞤆𞤢𞤳𞤭𞤧𞤼𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮",
          "PLN": "𞤔𞤢𞤤𞤮𞤼𞤵 𞤆𞤮𞤤𞤭𞥅𞤧𞤭𞤲𞤳𞤮",
          "PYG": "𞤘𞤵𞤱𞤢𞤪𞤢𞤲𞤭 𞤆𞤢𞥄𞤪𞤢𞤺𞤵𞤴𞤫𞤲𞤳𞤮",
          "QAR": "𞤈𞤭𞤴𞤢𞥄𞤤 𞤗𞤢𞤼𞤢𞤪𞤭𞤴𞤢𞤲𞤳𞤮",
          "RON": "𞤂𞤫𞤱𞤵 𞤈𞤮𞤥𞤢𞤲𞤭𞤲𞤳𞤮",
          "RSD": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤅𞤫𞤪𞤦𞤭𞤲𞤳𞤮",
          "RUB": "𞤈𞤵𞥅𞤦𞤮𞤤 𞤈𞤭𞥅𞤧𞤭𞤲𞤳𞤮",
          "RWF": "𞤊𞤢𞤪𞤢𞤲 𞤈𞤵𞤱𞤢𞤲𞤣𞤢𞤲𞤳𞤮",
          "SAR": "𞤈𞤭𞤴𞤢𞤤 𞤅𞤢𞤵𞥅𞤣𞤭𞤴𞤢𞤲𞤳𞤮",
          "SBD": "𞤁𞤢𞤤𞤢 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤅𞤵𞤤𞤢𞤴𞤥𞤢𞥄𞤲",
          "SCR": "𞤈𞤵𞤨𞤭𞥅 𞤅𞤫𞤴𞤧𞤭𞤤𞤭𞤲𞤳𞤮",
          "SDG": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤅𞤵𞤣𞤢𞤲𞤳𞤮",
          "SEK": "𞤑𞤪𞤮𞤲𞤢 𞤅𞤵𞤱𞤫𞤣𞤭𞤲𞤳𞤮",
          "SGD": "𞤁𞤢𞤤𞤢 𞤅𞤭𞤲𞤺𞤢𞤨𞤮𞤪𞤫𞤲𞤳𞤮",
          "SHP": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤅𞤫𞤲-𞤖𞤫𞤤𞤫𞤲𞤢",
          "SLL": "𞤂𞤫𞤴𞤮𞤲 𞤅𞤫𞤪𞤢𞤤𞤭𞤴𞤢𞤲𞤳𞤮",
          "SOS": "𞤅𞤭𞤤𞤭𞤲 𞤅𞤮𞤥𞤢𞤤𞤭𞤲𞤳𞤮",
          "SRD": "𞤁𞤢𞤤𞤢 𞤅𞤵𞤪𞤵𞤲𞤢𞤥𞤭𞤲𞤳𞤮",
          "SRG": "𞤘𞤭𞤤𞤣𞤮𞥅 𞤅𞤵𞤪𞤵𞤲𞤢𞤥𞤭𞤲𞤳𞤮",
          "SSP": "𞤆𞤢𞤱𞤲𞤣𞤵 𞤂𞤫𞤴𞤤𞤫𞤴𞤪𞤭 𞤅𞤵𞤣𞤢𞤲𞤭𞤲𞤳𞤮",
          "STN": "𞤁𞤮𞤦𞤢𞤪𞤢 𞤅𞤢𞤱𞤮-𞤚𞤮𞤥𞤫 & 𞤆𞤫𞤪𞤫𞤲𞤧𞤭𞤨",
          "SVC": "𞤑𞤮𞤤𞤮𞥅𞤲 𞤅𞤢𞤤𞤾𞤢𞤣𞤮𞤪𞤢𞤲𞤳𞤮",
          "SYP": "𞤆𞤢𞤱𞤲𞥋𞤣𞤵 𞤅𞤭𞤪𞤢𞤴𞤢𞤲𞤳𞤮",
          "SZL": "𞤂𞤭𞤤𞤢𞤲𞤺𞤫𞤲𞤭 𞤅𞤵𞤱𞤢𞤶𞤭",
          "THB": "𞤄𞤢𞤸𞤼𞤵 𞤚𞤢𞤴𞤤𞤢𞤲𞤣𞤭𞤲𞤳𞤮",
          "TJS": "𞤅𞤢𞤥𞤮𞥅𞤲𞤭 𞤚𞤢𞤶𞤭𞤳𞤭𞤧𞤼𞤢𞤲𞤳𞤮",
          "TMT": "𞤃𞤢𞤲𞤢𞤼𞤵 𞤚𞤵𞤪𞤳𞤵𞤥𞤫𞤲𞤭𞤧𞤼𞤢𞤲𞤳𞤮",
          "TND": "𞤁𞤭𞤲𞤢𞥄𞤪 𞤚𞤵𞥅𞤲𞤭𞤧𞤭𞤲𞤳𞤮",
          "TOP": "𞤆𞤢𞤢𞤲𞤺𞤢 𞤚𞤮𞤲𞤺𞤢𞤲𞤳𞤮",
          "TRY": "𞤂𞤭𞤪𞤢 𞤚𞤵𞤪𞤳𞤭𞤴𞤢𞤲𞤳𞤮",
          "TTD": "𞤁𞤢𞤤𞤢 𞤚𞤭𞤪𞤲𞤭𞤣𞤢𞥄𞤣 & 𞤚𞤮𞤦𞤢𞤺𞤮",
          "TWD": "𞤁𞤢𞤤𞤢 𞤚𞤢𞤴𞤱𞤢𞥄𞤲𞤳𞤮",
          "TZS": "𞤅𞤭𞤤𞤭𞤲 𞤚𞤢𞤲𞥁𞤢𞤲𞤭𞤲𞤳𞤮",
          "UAH": "𞤖𞤵𞤪𞤢𞤾𞤫𞤲𞤭𞤴𞤢 𞤒𞤵𞤳𞤫𞤪𞤫𞥅𞤲𞤭𞤲𞤳𞤮",
          "UGX": "𞤅𞤭𞤤𞤭𞤲 𞤓𞤺𞤢𞤲𞤣𞤢𞤲𞤳𞤮",
          "USD": "𞤁𞤢𞤤𞤢 𞤁𞤫𞤲𞤼𞤢𞤤 𞤂𞤢𞤪𞤫 𞤀𞤥𞤫𞤪𞤭𞤳",
          "USN": "𞤣𞤢𞤤𞤢 𞤁𞤂𞤀 (𞤶𞤢𞤲𞤺𞤮 𞤥𞤵𞥅𞤯𞤵𞤲)",
          "USS": "𞤣𞤢𞤤𞤢 𞤁𞤂𞤀 (𞤸𞤢𞤲𞤣𞤫 𞤥𞤵𞥅𞤯𞤵𞤲)",
          "UYI": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤒𞤵𞤪𞤺𞤮𞤴𞤢𞤲𞤳𞤮 (𞤕𞤢𞤤𞤯𞤭 𞤔𞤮𞥅𞤨𞤢𞥄𞤯𞤭)",
          "UYP": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤒𞤵𞤪𞤺𞤮𞤴𞤢𞤲𞤳𞤮 (𞥑𞥙𞥗𞥕-𞥑𞥙𞥙𞥓)",
          "UYU": "𞤆𞤫𞥅𞤧𞤮𞥅 𞤒𞤵𞤪𞤺𞤮𞤴𞤢𞤲𞤳𞤮",
          "UYW": "𞤅𞤢𞤤𞤲𞤣𞤵 𞤐𞤶𞤮𞤩𞤣𞤭 𞤒𞤵𞤪𞤺𞤮𞤴𞤢𞤲𞤳𞤮 𞤔𞤮𞥅𞤨𞤢𞥄𞤲𞤣𞤭",
          "UZS": "𞤅𞤮𞤥𞤵 𞤓𞥁𞤦𞤫𞤳𞤭𞤧𞤼𞤢𞤲𞤳𞤮",
          "VEB": "𞤄𞤮𞤤𞤭𞤾𞤢𞥄𞤪 𞤜𞤫𞤲𞤭𞥅𞤧𞤫𞤤𞤢𞤲𞤳𞤮 (𞥑𞥘𞥗𞥑-𞥒𞥐𞥐𞥘)",
          "VED": "𞤄𞤮𞤤𞤭𞤾𞤢𞥄𞤪 𞤅𞤮𞤦𞤫𞥊𞤪𞤢𞤲𞤮",
          "VEF": "𞤄𞤮𞤤𞤭𞤾𞤢𞥄𞤪 𞤜𞤫𞤲𞤭𞥅𞤧𞤫𞤤𞤢𞤲𞤳𞤮 (𞥒𞥐𞥐𞥘 - 𞥒𞥐𞥑𞥘)",
          "VES": "𞤄𞤮𞤤𞤭𞤾𞤢𞥄𞤪 𞤜𞤫𞤲𞤭𞥅𞤧𞤫𞤤𞤢𞤲𞤳𞤮",
          "VND": "𞤁𞤮𞤲𞤺𞤵 𞤜𞤭𞤴𞤫𞤼𞤭𞤲𞤢𞤴𞤢𞤲𞤳𞤮",
          "VUV": "𞤜𞤢𞤼𞤵 𞤜𞤢𞤲𞤵𞤴𞤢𞤲𞤳𞤮",
          "WST": "𞤚𞤢𞤤𞤢 𞤅𞤢𞤥𞤮𞤱𞤢𞤴𞤢𞤲𞤳𞤮",
          "XAF": "𞤊𞤢𞤪𞤢𞤲 𞤚𞤵𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤭𞤲𞤳𞤮",
          "XCD": "𞤁𞤢𞤤𞤢 𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞥋𞤺𞤫 𞤑𞤢𞤪𞤭𞤦𞤭𞤴𞤢",
          "XOF": "𞤊𞤢𞤪𞤢𞤲 𞤅𞤊𞤀 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢",
          "XPF": "𞤊𞤢𞤪𞤢𞤲 𞤅𞤊𞤆",
          "XXX": "𞤐𞤄𞤵𞥅𞤯𞤭 𞤢𞤧-𞤢𞤲𞤣𞤢𞥄𞤯𞤭",
          "YER": "𞤈𞤭𞤴𞤢𞥄𞤤 𞤒𞤫𞤥𞤫𞤲𞤭𞤲𞤳𞤮",
          "ZAR": "𞤈𞤢𞤲𞤣𞤭 𞤂𞤫𞤴𞤤𞤫𞤴𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞤲𞤳𞤮",
          "ZMW": "𞤑𞤢𞤱𞤢𞤧𞤢 𞤟𞤢𞤥𞤦𞤭𞤲𞤳𞤮"
        },
        "narrow": {
        },
        "short": {
        }
      },
      "dateTimeField": {
        "long": {
          "day": "𞤻𞤢𞤤𞤢𞥄𞤲𞤣𞤫",
          "dayOfYear": "𞤻𞤢𞤤𞤢𞥄𞤲𞤣𞤫 𞤲𞤣𞤫𞤪 𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫",
          "dayperiod": "𞤀𞤎/𞤇𞤎",
          "era": "𞤺𞤭𞤪𞤢𞤤",
          "hour": "𞤲𞤶𞤢𞤥𞤣𞤭",
          "minute": "𞤸𞤮𞤶𞤮𞤥𞤢𞥄𞤪𞤫",
          "month": "𞤤𞤫𞤱𞤪𞤵",
          "quarter": "𞤲𞤢𞤴𞤢𞤩𞤭𞥅𞤪𞤫",
          "second": "𞤳𞤭𞤲𞤰𞤫𞤪𞤫",
          "timeZoneName": "𞤲𞤶𞤢𞤥𞤣𞤭 𞤲𞤮𞤳𞥆𞤵𞥅𞤪𞤫",
          "weekOfMonth": "𞤴𞤮𞤲𞤼𞤫𞤪𞤫 𞤲𞤣𞤫𞤪 𞤤𞤫𞤱𞤪𞤵",
          "weekOfYear": "𞤴𞤮𞤲𞤼𞤫𞤪𞤫",
          "weekday": "𞤻𞤢𞤤𞤢𞥄𞤲𞤣𞤫 𞤲𞤣𞤫𞤪 𞤴𞤮𞤲𞤼𞤫𞤪𞤫",
          "weekdayOfMonth": "𞤻𞤢𞤤𞤢𞥄𞤲𞤣𞤫 𞤲𞤣𞤫𞤪 𞤴𞤮𞤲𞤼𞤫𞤪𞤫",
          "year": "𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫"
        },
        "narrow": {
          "day": "𞤻𞤢𞤤.",
          "dayOfYear": "𞤻𞤢𞤤. 𞤲𞤣𞤫𞤪 𞤸𞤼.",
          "dayperiod": "𞤀𞤎/𞤇𞤎",
          "era": "𞤺𞤭𞤪𞤢𞤤",
          "hour": "𞤶𞤢.",
          "minute": "𞤸𞤮𞤶.",
          "month": "𞤤𞤫𞤱.",
          "quarter": "𞤲𞤢𞤴.",
          "second": "𞤳𞤭𞤲.",
          "timeZoneName": "𞤲𞤶𞤢𞤥𞤣𞤭 𞤲𞤮𞤳𞥆𞤵𞥅𞤪𞤫",
          "weekOfMonth": "𞤴𞤼. 𞤲𞤣𞤫𞤪 𞤤𞤫𞤱.",
          "weekOfYear": "𞤴𞤼.",
          "weekday": "𞤻𞤢𞤤. 𞤲𞤣𞤫𞤪 𞤴𞤼.",
          "weekdayOfMonth": "𞤻𞤢𞤤. 𞤲𞤣𞤫𞤪 𞤤𞤫𞤱.",
          "year": "𞤸𞤭𞤼."
        },
        "short": {
          "day": "𞤻𞤢𞤤.",
          "dayOfYear": "𞤻𞤢𞤤. 𞤲𞤣𞤫𞤪 𞤸𞤼.",
          "dayperiod": "𞤀𞤎/𞤇𞤎",
          "era": "𞤺𞤭𞤪𞤢𞤤",
          "hour": "𞤶𞤢.",
          "minute": "𞤸𞤮𞤶.",
          "month": "𞤤𞤫𞤱.",
          "quarter": "𞤲𞤢𞤴.",
          "second": "𞤳𞤭𞤲.",
          "timeZoneName": "𞤲𞤮𞤳𞥆𞤵𞥅𞤪𞤫",
          "weekOfMonth": "𞤴𞤼. 𞤲𞤣𞤫𞤪 𞤤𞤫𞤱.",
          "weekOfYear": "𞤴𞤼.",
          "weekday": "𞤻𞤢𞤤. 𞤲𞤣𞤫𞤪 𞤴𞤼.",
          "weekdayOfMonth": "𞤻𞤢𞤤.𞤲𞤣𞤫𞤪 𞤤𞤫𞤱.",
          "year": "𞤸𞤭𞤼."
        }
      },
      "language": {
        "dialect": {
          "long": {
            "aa": "𞤀𞤬𞤢𞥄𞤪𞤫",
            "ab": "𞤀𞤦𞤳𞤢𞥄𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ace": "𞤀𞥄𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ada": "𞤀𞤣𞤢𞤲𞤺𞤥𞤫𞥅𞤪𞤫",
            "ady": "𞤀𞤣𞤭𞤿𞤭𞥅𞤪𞤫",
            "af": "𞤀𞤬𞤪𞤭𞤳𞤢𞤲𞤪𞤫",
            "agq": "𞤀𞤺𞤸𞤫𞤥𞤪𞤫",
            "ain": "𞤀𞤴𞤲𞤵𞥅𞤪𞤫",
            "ak": "𞤀𞤳𞤢𞤲𞤪𞤫",
            "ale": "𞤀𞤤𞤫𞤵𞤼𞤵𞥅𞤪𞤫",
            "alt": "𞤀𞤤𞤼𞤢𞤴𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮𞥅𞤪𞤫",
            "am": "𞤀𞤥𞤸𞤢𞤪𞤭𞥅𞤪𞤫",
            "an": "𞤀𞤪𞤢𞤺𞤮𞤲𞤪𞤫",
            "ann": "𞤌𞤦𞤮𞤤𞤮𞥅𞤪𞤫",
            "anp": "𞤀𞤲𞤺𞤭𞤳𞤢𞥄𞤪𞤫",
            "ar": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫",
            "ar-001": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫 𞤊𞤵𞤧𞤸𞤢 𞤒𞤫𞤲𞤯𞤵𞤳𞤢",
            "arn": "𞤃𞤢𞤨𞤵𞤷𞤭𞥅𞤪𞤫",
            "arp": "𞤀𞤪𞤢𞤨𞤢𞤸𞤮𞥅𞤪𞤫",
            "ars": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫 𞤐𞤢𞤶𞤣𞤭",
            "as": "𞤀𞤧𞤢𞤥𞤫𞥅𞤪𞤫",
            "asa": "𞤀𞤧𞤵𞥅𞤪𞤫",
            "ast": "𞤀𞤧𞤼𞤵𞤪𞤭𞥅𞤪𞤫",
            "atj": "𞤀𞤼𞤭𞤥𞤫𞤳𞤵𞤱𞤪𞤫",
            "av": "𞤀𞤬𞤱𞤢𞤪𞤭𞥅𞤪𞤫",
            "awa": "𞤀𞤱𞤢𞤣𞤭𞥅𞤪𞤫",
            "ay": "𞤀𞤴𞤥𞤢𞤪𞤢𞥄𞤪𞤫",
            "az": "𞤀𞤶𞤢𞤪𞤦𞤢𞤴𞤭𞤶𞤢𞤲𞤭𞥅𞤪𞤫",
            "ba": "𞤄𞤢𞤧𞤳𞤭𞥅𞤪𞤫",
            "ban": "𞤄𞤢𞥄𞤤𞤭𞥅𞤪𞤫",
            "bas": "𞤄𞤢𞤧𞤢𞥄𞤪𞤫",
            "be": "𞤄𞤫𞤤𞤢𞤪𞤭𞥅𞤧𞤭𞥅𞤪𞤫",
            "bem": "𞤄𞤫𞤥𞤦𞤢𞥄𞤪𞤫",
            "bez": "𞤄𞤫𞤲𞤢𞥄𞤪𞤫",
            "bg": "𞤄𞤭𞤤𞤺𞤢𞥄𞤪𞤫",
            "bho": "𞤄𞤮𞤧𞤨𞤵𞤪𞤭𞥅𞤪𞤫",
            "bi": "𞤄𞤭𞤧𞤤𞤢𞤥𞤢𞥄𞤪𞤫",
            "bin": "𞤄𞤭𞤲𞤭𞥅𞤪𞤫",
            "bla": "𞤅𞤭𞤳𞤧𞤭𞤳𞤢𞥄𞤪𞤫",
            "bm": "𞤄𞤢𞤥𞤦𞤢𞤪𞤢𞥄𞤪𞤫",
            "bn": "𞤄𞤫𞤲𞤺𞤢𞤤𞤭𞥅𞤪𞤫",
            "bo": "𞤚𞤭𞤦𞤫𞤼𞤫𞤲𞤪𞤫",
            "br": "𞤄𞤫𞤪𞤫𞤼𞤮𞤲𞤪𞤫",
            "brx": "𞤄𞤮𞤣𞤮𞥅𞤪𞤫",
            "bs": "𞤄𞤮𞤧𞤲𞤭𞤴𞤢𞥄𞤪𞤫",
            "bug": "𞤄𞤵𞤺𞤭𞤧𞤢𞥄𞤪𞤫",
            "byn": "𞤄𞤭𞤤𞤭𞤲𞤪𞤫",
            "ca": "𞤑𞤢𞤼𞤢𞤤𞤢𞤲𞤪𞤫",
            "cay": "𞤑𞤢𞤴𞤺𞤢𞥄𞤪𞤫",
            "ccp": "𞤅𞤢𞤳𞤥𞤢𞥄𞤪𞤫",
            "ce": "𞤕𞤫𞤷𞤫𞤲𞤪𞤫",
            "ceb": "𞤅𞤫𞤦𞤱𞤢𞤲𞤮𞥅𞤪𞤫",
            "cgg": "𞤕𞤭𞤺𞤢𞥄𞤪𞤫",
            "ch": "𞤕𞤢𞤥𞤮𞤪𞤮𞥅𞤪𞤫",
            "chk": "𞤕𞤵𞥅𞤳𞤵𞥅𞤪𞤫",
            "chm": "𞤃𞤢𞤪𞤭𞥅𞤪𞤫",
            "cho": "𞤕𞤢𞤸𞤼𞤢𞥄𞤪𞤫",
            "chp": "𞤕𞤭𞤨𞤴𞤢𞤲𞤪𞤫",
            "chr": "𞤕𞤫𞥅𞤪𞤮𞤳𞤭𞥅𞤪𞤫",
            "chy": "𞤅𞤢𞥄𞤴𞤢𞤲𞤪𞤫",
            "ckb": "𞤑𞤵𞤪𞤣𞤵𞥅𞤪𞤫",
            "clc": "𞤕𞤭𞤤𞤳𞤮𞤼𞤭𞤲𞤪𞤫",
            "co": "𞤑𞤮𞤪𞤧𞤭𞤳𞤢𞥄𞤪𞤫",
            "crg": "𞤃𞤭𞤷𞤭𞤬𞤪𞤫",
            "crj": "𞤑𞤪𞤭𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "crk": "𞤆𞤤𞤫𞤭𞤲𞤧 𞤑𞤪𞤭𞥅𞤪𞤫",
            "crl": "Vote 𞤑𞤪𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞤬𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "crm": "𞤃𞤮𞥅𞤧𞤫 𞤑𞤪𞤭𞥅𞤪𞤫",
            "crr": "𞤀𞤤𞤺𞤮𞤲𞤳𞤭𞤲𞤪𞤫 𞤑𞤢𞥄𞤪𞤤𞤭𞤲𞤢",
            "cs": "𞤕𞤫𞤳𞤧𞤭𞤲𞤢𞥄𞤪𞤫",
            "csw": "𞤑𞤪𞤭𞥅𞤪𞤫 𞤅𞤢𞤱𞤨𞤭𞥅",
            "cu": "𞤅𞤭𞤤𞤾𞤭𞤳𞤪𞤫 𞤕𞤮𞥅𞤷𞤭",
            "cv": "𞤕𞤵𞥅𞤾𞤢𞤧𞤪𞤫",
            "cy": "𞤘𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "da": "𞤁𞤢𞥄𞤲𞤭𞤧𞤳𞤮𞥅𞤪𞤫",
            "dak": "𞤁𞤢𞤳𞤮𞤼𞤢𞥄𞤪𞤫",
            "dar": "𞤁𞤢𞤪𞤺𞤭𞤲𞤢𞥄𞤪𞤫",
            "dav": "𞤚𞤢𞤭𞤼𞤢𞥄𞤪𞤫",
            "de": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "de-AT": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤌𞤼𞤭𞤪𞤧𞤢",
            "de-CH": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤅𞤵𞤱𞤭𞥅𞤧",
            "dgr": "𞤁𞤮𞤺𞤪𞤭𞤦𞤪𞤫",
            "dje": "𞤔𞤢𞤪𞤥𞤢𞥄𞤪𞤫",
            "doi": "𞤁𞤮𞤺𞤪𞤭𞥅𞤪𞤫",
            "dsb": "𞤂𞤫𞤧 𞤅𞤮𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫",
            "dua": "𞤁𞤵𞤱𞤢𞤤𞤢𞥄𞤪𞤫",
            "dv": "𞤁𞤭𞥅𞤬𞤫𞤸𞤭𞥅𞤪𞤫",
            "dyo": "𞤔𞤮𞥅𞤤𞤢𞥄𞤪𞤫",
            "dz": "𞤄𞤵𞥅𞤼𞤢𞤲𞤪𞤫",
            "dzg": "𞤁𞤢𞤶𞤢𞤺𞤢𞥄𞤪𞤫",
            "ebu": "𞤉𞤥𞤦𞤵𞥅𞤪𞤫",
            "ee": "𞤉𞤱𞤫𞥅𞤪𞤫",
            "efi": "𞤉𞤬𞤭𞤳𞤪𞤫",
            "eka": "𞤉𞤳𞤢𞤶𞤵𞤳𞤪𞤫",
            "el": "𞤘𞤭𞥅𞤪𞤧𞤢𞥄𞤪𞤫",
            "en": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫",
            "en-AU": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "en-CA": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤑𞤢𞤲𞤢𞤣𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "en-GB": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤄𞤭𞤪𞤼𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "en-US": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞤲𞤳𞤮𞤪𞤫",
            "eo": "𞤉𞤧𞤨𞤫𞤪𞤢𞤲𞤼𞤮𞥅𞤪𞤫",
            "es": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "es-419": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤀𞤥𞤭𞤪𞤭𞤳 𞤂𞤢𞤼𞤭𞤲𞤭𞤴𞤢",
            "es-ES": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤀𞤪𞤮𞤦𞤢",
            "es-MX": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤃𞤫𞤳𞤧𞤭𞤳",
            "et": "𞤉𞤧𞤼𞤮𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "eu": "𞤄𞤢𞤧𞤳𞤢𞤪𞤢𞥄𞤪𞤫",
            "ewo": "𞤉𞤱𞤮𞤲𞤣𞤮𞥅𞤪𞤫",
            "fa": "𞤊𞤢𞥄𞤪𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "fa-AF": "𞤁𞤢𞤪𞤭𞥅𞤪𞤫",
            "ff": "𞤆𞤵𞤤𞤢𞤪",
            "fi": "𞤊𞤫𞤲𞤭𞤧𞤪𞤫",
            "fil": "𞤊𞤭𞤤𞤭𞤨𞤭𞤲𞤮𞥅𞤪𞤫",
            "fj": "𞤊𞤭𞥅𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "fo": "𞤊𞤫𞤪𞤮𞤱𞤫𞤧𞤪𞤫",
            "fon": "𞤊𞤮𞤲𞤪𞤫",
            "fr": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫",
            "fr-CA": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 𞤑𞤢𞤲𞤢𞤣𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "fr-CH": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 𞤅𞤵𞤱𞤭𞥅𞤧",
            "frc": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 𞤑𞤢𞤣𞤭𞤴𞤫𞤲𞤪𞤫",
            "fur": "𞤊𞤭𞤪𞥇𞤵𞤤𞤭𞤴𞤢𞤲𞤪𞤫",
            "fy": "𞤊𞤭𞤪𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤖𞤭𞤪𞤲𞤢",
            "ga": "𞤋𞤪𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "gaa": "𞤘𞤢𞥄𞤪𞤫",
            "gd": "𞤅𞤭𞤳𞤮𞤼𞤭𞤧𞤪𞤫 𞤘𞤢𞤫𞤭𞤳",
            "gez": "𞤘𞤫𞥅𞤶𞤪𞤫",
            "gil": "𞤘𞤭𞤤𞤦𞤫𞤪𞤼𞤫𞥅𞤧𞤪𞤫",
            "gl": "𞤘𞤢𞤤𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "gn": "𞤘𞤵𞤢𞤪𞤢𞤲𞤭𞥅𞤪𞤫",
            "gor": "𞤘𞤮𞤪𞤮𞤲𞤼𞤢𞤤𞤮𞥅𞤪𞤫",
            "gsw": "𞤔𞤫𞤪𞤥𞤢𞤲𞤪𞤫 𞤅𞤵𞤱𞤭𞤧",
            "gu": "𞤘𞤵𞤶𞤢𞤪𞤢𞤼𞤭𞥅𞤪𞤫",
            "guz": "𞤘𞤵𞤧𞤭𞥅𞤪𞤫",
            "gv": "𞤃𞤢𞤲𞤳𞤭𞤧𞤪𞤫",
            "gwi": "𞤘𞤭𞤱𞤧𞤭𞤲𞤪𞤫",
            "ha": "𞤖𞤢𞤱𞤧𞤢𞥄𞤪𞤫",
            "hai": "𞤖𞤢𞤴𞤣𞤢𞥄𞤪𞤫",
            "haw": "𞤖𞤢𞤱𞤢𞥄𞤭𞤴𞤫𞤲𞤪𞤫",
            "hax": "𞤖𞤢𞤭𞤣𞤢𞥄𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "he": "𞤖𞤭𞤦𞤵𞤪𞤵𞥅𞤪𞤫",
            "hi": "𞤖𞤭𞤲𞤣𞤭𞥅𞤪𞤫",
            "hil": "𞤖𞤭𞤤𞤭𞤺𞤢𞤴𞤲𞤮𞤲𞤪𞤫",
            "hmn": "𞤖𞤵𞤥𞤺𞤵𞤲𞤪𞤫",
            "hr": "𞤑𞤮𞤪𞤮𞤱𞤢𞤧𞤭𞥅𞤪𞤫",
            "hsb": "𞤅𞤮𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫 𞤁𞤮𞤱𞤪𞤭",
            "ht": "𞤀𞤳𞤵𞥅𞤪𞤫 𞤖𞤢𞤴𞤼𞤭𞥅",
            "hu": "𞤖𞤵𞤲𞤺𞤢𞤪𞤭𞤴𞤢𞤲𞤪𞤫",
            "hup": "𞤖𞤵𞤨𞤢𞥄𞤪𞤫",
            "hur": "𞤖𞤢𞤤𞤳𞤮𞤥𞤫𞤤𞤫𞤥𞤪𞤫",
            "hy": "𞤀𞤪𞤥𞤫𞤲𞤭𞥅𞤪𞤫",
            "hz": "𞤖𞤫𞤪𞤫𞤪𞤮𞥅𞤪𞤫",
            "ia": "𞤉𞤲𞤼𞤫𞤪𞤤𞤭𞤺𞤢𞥄𞤪𞤫",
            "iba": "𞤋𞤦𞤢𞤲𞤪𞤫",
            "ibb": "𞤋𞤦𞤭𞥅𞤦𞤭𞤴𞤮𞥅𞤪𞤫",
            "id": "𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "ig": "𞤋𞤦𞤮𞥅𞤪𞤫",
            "ii": "𞤅𞤭𞤧𞤵𞤢𞤲𞤪𞤫 𞤒𞤭𞥅",
            "ikt": "𞤋𞤲𞤵𞤳𞤼𞤵𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤑𞤢𞤲𞤢𞤣𞤢𞥄",
            "ilo": "𞤋𞤤𞤮𞤳𞤮𞥅𞤪𞤫",
            "inh": "𞤋𞤲𞤺𞤮𞤧𞤫𞥅𞤪𞤫",
            "io": "𞤋𞤣𞤮𞥅𞤪𞤫",
            "is": "𞤀𞤴𞤧𞤭𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "it": "𞤋𞤼𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "iu": "𞤋𞤲𞤵𞤳𞤼𞤫𞥅𞤪𞤫",
            "ja": "𞤐𞤭𞤨𞤮𞤲𞤪𞤫",
            "jbo": "𞤂𞤮𞤶𞤦𞤢𞤲𞤪𞤫",
            "jgo": "𞤐𞤺𞤮𞤥𞤦𞤢𞥄𞤪𞤫",
            "jmc": "𞤃𞤢𞤳𞤢𞤥𞤫𞥅𞤪𞤫",
            "jv": "𞤔𞤢𞥄𞤱𞤢𞤫𞥅𞤪𞤫",
            "ka": "𞤔𞤮𞥅𞤪𞥁𞤭𞤴𞤢𞤲𞤪𞤫",
            "kab": "𞤑𞤢𞤦𞤭𞤤𞤭𞥅𞤪𞤫",
            "kac": "𞤑𞤢𞤧𞤭𞤲𞤪𞤫",
            "kaj": "𞤑𞤢𞤶𞤫𞥅𞤪𞤫",
            "kam": "𞤑𞤢𞤥𞤦𞤢𞥄𞤪𞤫",
            "kbd": "𞤑𞤢𞤦𞤢𞤪𞤣𞤭𞤴𞤢𞤲𞤪𞤫",
            "kcg": "𞤚𞤵𞤴𞤢𞤨𞤵𞥅𞤪𞤫",
            "kde": "𞤃𞤢𞤳𞤮𞤲𞤣𞤫𞥅𞤪𞤫",
            "kea": "𞤑𞤢𞤦𞤵𞤾𞤫𞤪𞤣𞤭𞤴𞤢𞤲𞤪𞤫",
            "kfo": "𞤑𞤮𞤪𞤮𞥅𞤪𞤫",
            "kgp": "𞤑𞤢𞤭𞤲𞤺𞤢𞤲𞤺𞤪𞤫",
            "kha": "𞤝𞤢𞤧𞤭𞥅𞤪𞤫",
            "khq": "𞤑𞤮𞤴𞤪𞤢𞤷𞤭𞤲𞤪𞤫",
            "ki": "𞤑𞤭𞤳𞤵𞤴𞤵𞥅𞤪𞤫",
            "kj": "𞤑𞤵𞤢𞤻𞤢𞤥𞤢𞥄𞤪𞤫",
            "kk": "𞤑𞤢𞥁𞤢𞤳𞤪𞤫",
            "kkj": "𞤑𞤢𞤳𞤮𞥅𞤪𞤫",
            "kl": "𞤑𞤢𞤤𞤢𞥄𞤤𞤧𞤵𞤼𞤪𞤫",
            "kln": "𞤑𞤢𞤤𞤫𞤲𞤶𞤭𞤲𞤪𞤫",
            "km": "𞤑𞤵𞤥𞤢𞤴𞤪𞤫",
            "kmb": "𞤑𞤭𞤥𞤦𞤵𞤲𞤣𞤵𞥅𞤪𞤫",
            "kn": "𞤑𞤢𞤲𞥆𞤢𞤣𞤢𞥄𞤪𞤫",
            "ko": "𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞤲𞤪𞤫",
            "kok": "𞤑𞤮𞤲𞤳𞤢𞤲𞤭𞥅𞤪𞤫",
            "kpe": "𞤘𞤫𞤪𞤧𞤫𞥅𞤪𞤫",
            "kr": "𞤑𞤮𞥅𞤤𞤫𞥅𞤪𞤫",
            "krc": "𞤑𞤢𞤪𞤢𞤧𞤢𞤴-𞤄𞤢𞤤𞤳𞤢𞥄𞤪𞤫",
            "krl": "𞤑𞤢𞤪𞤫𞤤𞤭𞤢𞤲𞤪𞤫",
            "kru": "𞤑𞤵𞤪𞤵𞤿𞤵𞥅𞤪𞤫",
            "ks": "𞤑𞤢𞥃𞤥𞤭𞥅𞤪𞤫",
            "ksb": "𞤅𞤢𞤥𞤦𞤢𞤤𞤢𞥄𞤪𞤫",
            "ksf": "𞤄𞤢𞤬𞤭𞤴𞤢𞥄𞤪𞤫",
            "ksh": "𞤑𞤮𞤤𞤮𞤺𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "ku": "𞤑𞤵𞤪𞤣𞤭𞤧𞤭𞥅𞤪𞤫",
            "kum": "𞤑𞤵𞤥𞤴𞤢𞤳𞤪𞤫",
            "kv": "𞤑𞤮𞤥𞤭𞥅𞤪𞤫",
            "kw": "𞤑𞤮𞤪𞤲𞤭𞥅𞤪𞤫",
            "kwk": "𞤑𞤢𞤱𞤳𞥇𞤱𞤢𞤤𞤢𞥄𞤪𞤫",
            "ky": "𞤑𞤭𞤪𞤺𞤵𞥅𞤪𞤫",
            "la": "𞤂𞤢𞤼𞤫𞤲𞤪𞤫",
            "lad": "𞤂𞤢𞤣𞤭𞤲𞤮𞥅𞤪𞤫",
            "lag": "𞤂𞤢𞤲𞤺𞤭𞥅𞤪𞤫",
            "lb": "𞤂𞤵𞥁𞤫𞤲𞤦𞤵𞥅𞤪𞤺𞤭𞤧𞤪𞤫",
            "lez": "𞤂𞤫𞥁𞤺𞤭𞤴𞤢𞤲𞤪𞤫",
            "lg": "𞤘𞤢𞤲𞤣𞤢𞥄𞤪𞤫",
            "li": "𞤂𞤭𞤥𞤦𞤵𞤪𞤺𞤵𞤧𞤪𞤫",
            "lij": "𞤂𞤳𞤭𞤺𞤵𞥅𞤪𞤫",
            "lil": "𞤂𞤭𞤤𞥆𞤮𞥅𞤫𞤼𞤪𞤫",
            "lkt": "𞤂𞤢𞤳𞤮𞤼𞤢𞥄𞤪𞤫",
            "ln": "𞤂𞤭𞤲𞤺𞤢𞤤𞤢𞥄𞤪𞤫",
            "lo": "𞤂𞤢𞤮𞥅𞤪𞤫",
            "lou": "𞤀𞤳𞤵𞥅𞤪𞤫 𞤂𞤵𞥅𞥁𞤭𞤴𞤢𞥄𞤲𞤢",
            "loz": "𞤂𞤮𞥁𞤭𞥅𞤪𞤫",
            "lrc": "𞤂𞤵𞤪𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "lsm": "𞤅𞤢𞥄𞤥𞤭𞤢𞥄𞤪𞤫",
            "lt": "𞤂𞤭𞤼𞤮𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "lu": "𞤂𞤵𞤦𞤢-𞤑𞤢𞤼𞤢𞤲𞤺𞤢𞥄𞤪𞤫",
            "lua": "𞤂𞤵𞤦𞤢 𞤑𞤢𞤧𞤢𞤭𞤪𞤫",
            "lun": "𞤂𞤵𞤲𞤣𞤢𞥄𞤪𞤫",
            "luo": "𞤂𞤵𞤮𞥅𞤪𞤫",
            "lus": "𞤃𞤭𞤧𞤮𞥅𞤪𞤫",
            "luy": "𞤂𞤵𞤴𞤭𞤢𞥄𞤪𞤫",
            "lv": "𞤂𞤢𞤼𞤾𞤭𞤴𞤢𞤲𞤪𞤫",
            "mad": "𞤃𞤢𞤣𞤵𞤪𞤫𞥅𞤪𞤫",
            "mag": "𞤃𞤢𞤺𞤢𞤸𞤭𞥅𞤪𞤫",
            "mai": "𞤃𞤢𞤴𞤭𞤼𞤭𞤤𞤭𞥅𞤪𞤫",
            "mak": "𞤃𞤢𞤳𞤢𞤧𞤢𞤪𞤢𞥄𞤪𞤫",
            "mas": "𞤃𞤢𞤧𞤢𞤴𞤭𞥅𞤪𞤫",
            "mdf": "𞤃𞤮𞤳𞤧𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "men": "𞤃𞤫𞤲𞤣𞤫𞥅𞤪𞤫",
            "mer": "𞤃𞤫𞤪𞤵𞥅𞤪𞤫",
            "mfe": "𞤃𞤮𞤪𞤭𞥅𞤧𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mg": "𞤃𞤢𞤤𞤢𞤺𞤢𞤧𞤭𞥅𞤪𞤫",
            "mgh": "𞤃𞤢𞤳𞤵𞤱𞤢𞥄𞤪𞤫",
            "mgo": "𞤃𞤫𞤼𞤢𞥄𞤪𞤫",
            "mh": "𞤃𞤢𞤪𞤧𞤢𞤤𞤫𞥅𞤪𞤫",
            "mi": "𞤃𞤢𞥄𞤮𞤪𞤭𞥅𞤪𞤫",
            "mic": "𞤃𞤭𞤳𞤥𞤢𞤹𞤵𞥅𞤪𞤫",
            "min": "𞤃𞤭𞤲𞤢𞤲𞤺𞤳𞤢𞤦𞤢𞤵𞥅𞤪𞤫",
            "mk": "𞤃𞤢𞤧𞤫𞤣𞤮𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "ml": "𞤃𞤢𞤤𞤢𞤴𞤢𞤤𞤢𞤥𞤪𞤫",
            "mn": "𞤃𞤮𞤲𞤺𞤮𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mni": "𞤃𞤢𞤲𞤭𞤨𞤵𞥅𞤪𞤫",
            "moe": "𞤋𞤲𞥆𞤵-𞤢𞤴𞤥𞤵𞤲𞤪𞤫",
            "moh": "𞤃𞤮𞥅𞤸𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "mos": "𞤃𞤮𞥅𞤧𞤭𞥅𞤪𞤫",
            "mr": "𞤃𞤢𞤪𞤢𞤼𞤭𞥅𞤪𞤫",
            "ms": "𞤃𞤢𞤤𞤫𞥅𞤪𞤫",
            "mt": "𞤃𞤢𞤤𞤼𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mua": "𞤃𞤵𞤲𞤣𞤢𞤲𞤪𞤫",
            "mul": "𞤍𞤫𞤲𞤯𞤫 𞤅𞤫𞤪𞤼𞤵𞤯𞤫",
            "mus": "𞤃𞤵𞤧𞤳𞤮𞤳𞤭𞥅𞤪𞤫",
            "mwl": "𞤃𞤭𞤪𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "my": "𞤄𞤵𞤪𞤥𞤢𞥄𞤪𞤫",
            "myv": "𞤉𞤪𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "mzn": "𞤃𞤢𞥁𞤢𞤲𞤣𞤫𞤪𞤢𞤲𞤭𞥅𞤪𞤫",
            "na": "𞤐𞤢𞤱𞤵𞤪𞤵𞤲𞤳𞤮𞥅𞤪𞤫",
            "nap": "𞤐𞤢𞥄𞤨𞤮𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "naq": "𞤐𞤢𞤥𞤢𞥄𞤪𞤫",
            "nb": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫 𞤄𞤮𞤳𞤥𞤢𞤤",
            "nd": "𞤐𞤣𞤫𞤦𞤫𞤤𞤫𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤺𞤫",
            "nds": "𞤂𞤫𞤧-𞤀𞤤𞤵𞤥𞤢𞤲𞤪𞤫",
            "nds-NL": "𞤂𞤫𞤧 𞤅𞤢𞤳𞤧𞤮𞤲𞤪𞤫",
            "ne": "𞤐𞤫𞤨𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "new": "𞤐𞤫𞤱𞤢𞤪𞤭𞥅𞤪𞤫",
            "ng": "𞤐𞤣𞤮𞤲𞤺𞤢𞥄𞤪𞤫",
            "nia": "𞤙𞤢𞤧𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "niu": "𞤐𞤭𞤵𞤫𞤴𞤢𞤲𞤪𞤫",
            "nl": "𞤁𞤮𞥅𞤷𞤵𞤪𞤫",
            "nl-BE": "𞤊𞤭𞤤𞤢𞤥𞤢𞤲𞤪𞤫",
            "nmg": "𞤐𞤺𞤵𞤥𞤦𞤢𞥄𞤪𞤫",
            "nn": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫 𞤙𞤮𞤪𞤧𞤳",
            "nnh": "𞤐𞤶𞤢𞤥𞤦𞤵𞥅𞤪𞤫",
            "no": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "nog": "𞤐𞤮𞤺𞤢𞤭𞥅𞤪𞤫",
            "nqo": "𞤐𞤳𞤮𞥅𞤪𞤫",
            "nr": "𞤐𞤣𞤫𞤦𞤫𞤤𞤫𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫",
            "nso": "𞤅𞤮𞤼𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "nus": "𞤐𞤵𞤫𞤪𞤭𞥅𞤪𞤫",
            "nv": "𞤐𞤢𞤬𞤱𞤢𞤸𞤮𞥅𞤪𞤫",
            "ny": "𞤙𞤢𞤲𞤶𞤢𞥄𞤪𞤫",
            "nyn": "𞤙𞤢𞤲𞤳𞤮𞤤𞤫𞥅𞤪𞤫",
            "oc": "𞤌𞤷𞥆𞤭𞤼𞤢𞤲𞤪𞤫",
            "ojb": "𞤌𞤶𞤭𞤦𞤵𞤱𞤢𞥄𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "ojc": "𞤌𞤶𞤭𞤦𞤵𞤱𞤪𞤫 𞤕𞤢𞤳𞤢",
            "ojs": "𞤌𞤶𞤭-𞤑𞤪𞤭𞥅𞤪𞤫",
            "ojw": "𞤌𞤶𞤭𞤦𞤱𞤢𞥄𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫",
            "oka": "𞤌𞤳𞤢𞤲𞤢𞤺𞤢𞤲𞤪𞤫",
            "om": "𞤌𞤪𞤮𞤥𞤮𞥅𞤪𞤫",
            "or": "𞤌𞤣𞤭𞤢𞥄𞤪𞤫",
            "os": "𞤌𞤧𞥆𞤫𞤼𞤭𞤳𞤪𞤫",
            "pa": "𞤆𞤵𞤲𞤶𞤢𞥄𞤦𞤭𞥅𞤪𞤫",
            "pag": "𞤆𞤢𞤲𞤺𞤢𞤧𞤭𞤲𞤢𞤲𞤪𞤫",
            "pam": "𞤆𞤢𞤥𞤨𞤢𞤲𞤺𞤢𞥄𞤪𞤫",
            "pap": "𞤆𞤢𞤨𞤭𞤢𞤥𞤫𞤲𞤼𞤮𞥅𞤪𞤫",
            "pau": "𞤆𞤢𞤤𞤢𞤵𞤴𞤢𞤲𞤪𞤫",
            "pcm": "𞤆𞤭𞤶𞤫𞤲𞤪𞤫 𞤐𞤢𞤶𞤭𞤪𞤭𞤴𞤢𞥄",
            "pis": "𞤆𞤭𞤶𞤭𞤲𞤪𞤫",
            "pl": "𞤆𞤮𞤤𞤢𞤲𞤣𞤭𞥅𞤪𞤫",
            "pqm": "𞤃𞤢𞤤𞤭𞤧𞤫𞥅𞤼-𞤆𞤢𞤧𞤢𞤥𞤢𞤹𞤵𞤮𞤣𞥆𞤭",
            "prg": "𞤆𞤵𞤪𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ps": "𞤆𞤢𞤧𞤼𞤵𞤲𞤪𞤫",
            "pt": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫",
            "pt-BR": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫 𞤄𞤪𞤫𞥁𞤭𞤤",
            "pt-PT": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫 𞤆𞤮𞤪𞤼𞤭𞤺𞤢𞥄𞤤",
            "qu": "𞤗𞤵𞤷𞤵𞤢𞤲𞤪𞤫",
            "rap": "𞤈𞤢𞤨𞤢𞤲𞤵𞤭𞥅𞤪𞤫",
            "rar": "𞤈𞤢𞤪𞤮𞤼𞤮𞤲𞤺𞤢𞤲𞤪𞤫",
            "rhg": "𞤈𞤮𞤸𞤭𞤲𞤺𞤢𞥄𞤪𞤫",
            "rm": "𞤈𞤮𞤥𞤢𞤲𞤧𞤪𞤫",
            "rn": "𞤈𞤵𞤲𞤣𞤭𞥅𞤪𞤫",
            "ro": "𞤈𞤮𞤥𞤢𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "ro-MD": "𞤃𞤮𞤤𞤣𞤢𞤾𞤭𞤴𞤢𞤲𞤪𞤫",
            "rof": "𞤈𞤮𞤥𞤦𞤮𞥅𞤪𞤫",
            "ru": "𞤈𞤮𞥅𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "rup": "𞤀𞤪𞤮𞤥𞤢𞤲𞤭𞥅𞤪𞤫",
            "rw": "𞤑𞤭𞤻𞤭𞤪𞤵𞤱𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "rwk": "𞤈𞤵𞤱𞤢𞥄𞤪𞤫",
            "sa": "𞤅𞤢𞤲𞤧𞤳𞤪𞤭𞤼𞤪𞤫",
            "sad": "𞤅𞤢𞤲𞤣𞤢𞤱𞤫𞥅𞤪𞤫",
            "sah": "𞤅𞤢𞤿𞤢𞥄𞤪𞤫",
            "saq": "𞤅𞤢𞤥𞤦𞤵𞤪𞤵𞥅𞤪𞤫",
            "sat": "𞤅𞤢𞤲𞤼𞤢𞤤𞤭𞥅𞤪𞤫",
            "sba": "𞤐𞤺𞤢𞤥𞤦𞤢𞤴𞤪𞤫",
            "sbp": "𞤅𞤢𞤲𞤺𞤵𞥅𞤪𞤫",
            "sc": "𞤅𞤢𞤪𞤣𞤭𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "scn": "𞤅𞤭𞤧𞤭𞤤𞤭𞤴𞤢𞤲𞤪𞤫",
            "sco": "𞤅𞤭𞤳𞤮𞤼𞤧𞤪𞤫",
            "sd": "𞤅𞤭𞤲𞤣𞤢𞥄𞤪𞤫",
            "se": "𞤅𞤢𞤥𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "seh": "𞤅𞤫𞤲𞤢𞥄𞤪𞤫",
            "ses": "𞤑𞤮𞤪𞤮𞤦𞤮𞤪𞤮𞥅𞤪𞤫 𞤅𞤫𞤲𞥆𞤭",
            "sg": "𞤅𞤢𞤲𞤺𞤮𞥅𞤪𞤫",
            "shi": "𞤚𞤢𞤧𞤭𞤤𞤸𞤭𞤼𞤪𞤫",
            "shn": "𞤅𞤢𞤲𞤪𞤫",
            "si": "𞤅𞤭𞤲𞤸𞤢𞤤𞤢𞥄𞤪𞤫",
            "sk": "𞤅𞤤𞤮𞤾𞤢𞥄𞤳𞤪𞤫",
            "sl": "𞤅𞤤𞤮𞤾𞤫𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "slh": "𞤂𞤵𞥃𞤵𞤼𞤧𞤭𞤣𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "sm": "𞤅𞤢𞤥𞤮𞤢𞤲𞤪𞤫",
            "smn": "𞤋𞤲𞤢𞤪𞤭𞤧𞤳𞤢𞤤𞤭𞥅𞤪𞤫",
            "sms": "𞤅𞤭𞤳𞤮𞤤𞤼 𞤅𞤢𞤥𞤭𞥅𞤪𞤫",
            "sn": "𞤅𞤮𞤲𞤢𞥄𞤪𞤫",
            "snk": "𞤅𞤢𞤪𞤢𞤲𞤳𞤵𞤤𞥆𞤪𞤫",
            "so": "𞤅𞤮𞤥𞤢𞤤𞤭𞥅𞤪𞤫",
            "sq": "𞤀𞤤𞤦𞤢𞤲𞤭𞥅𞤪𞤫",
            "sr": "𞤅𞤫𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫",
            "srn": "𞤅𞤢𞤪𞤲𞤢𞤲-𞤚𞤮𞤲𞤺𞤮𞥅𞤪𞤫",
            "ss": "𞤅𞤵𞤱𞤢𞤼𞤭𞥅𞤪𞤫",
            "st": "𞤅𞤮𞤼𞤮𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "str": "𞤅𞤭𞤼𞤪𞤭𞤼 𞤅𞤢𞤤𞤭𞤧𞤪𞤫",
            "su": "𞤅𞤵𞤲𞤣𞤢𞤲𞤭𞥅𞤪𞤫",
            "suk": "𞤅𞤵𞤳𞤵𞤥𞤢𞥄𞤪𞤫",
            "sv": "𞤅𞤱𞤫𞤣𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "sw": "𞤅𞤵𞤱𞤢𞤸𞤭𞤤𞤭𞥅𞤪𞤫",
            "sw-CD": "𞤅𞤵𞤱𞤢𞤸𞤭𞤤𞤭𞥅𞤪𞤫 𞤑𞤮𞤲𞤺𞤮 𞤑𞤭𞤲𞤧𞤢𞤧𞤢",
            "swb": "𞤑𞤮𞤥𞤮𞤪𞤭𞥅𞤪𞤫",
            "syr": "𞤅𞤭𞥅𞤪𞤭𞤴𞤢𞤳𞤪𞤫",
            "ta": "𞤚𞤢𞤥𞤵𞤤𞤪𞤫",
            "tce": "𞤚𞤵𞤼𞤳𞤮𞤲𞤭𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫",
            "te": "𞤚𞤫𞤤𞤫𞤺𞤵𞥅𞤪𞤫",
            "tem": "𞤚𞤫𞤥𞤫𞤲𞤫𞥅𞤪𞤫",
            "teo": "𞤚𞤫𞤧𞤮𞥅𞤪𞤫",
            "tet": "𞤚𞤫𞤼𞤵𞤥𞤪𞤫",
            "tg": "𞤚𞤢𞤶𞤭𞤳𞤪𞤫",
            "tgx": "𞤚𞤢𞤺𞤭𞥃𞤪𞤫",
            "th": "𞤚𞤢𞤴𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "tht": "𞤚𞤢𞤸𞤢𞤤𞤼𞤢𞤲𞤪𞤫",
            "ti": "𞤚𞤭𞤺𞤭𞤪𞤻𞤢𞥄𞤪𞤫",
            "tig": "𞤚𞤭𞤺𞤭𞤪𞤴𞤢𞤲𞤪𞤫",
            "tk": "𞤼𞤵𞤪𞤳𞤥𞤢𞤲𞤪𞤫",
            "tlh": "𞤑𞤭𞤤𞤭𞤲𞤺𞤮𞤲𞤪𞤫",
            "tli": "𞤚𞤤𞤭𞤲𞤺𞤭𞤼𞤪𞤫",
            "tn": "𞤚𞤭𞤧𞤱𞤢𞤲𞤢𞥄𞤪𞤫",
            "to": "𞤚𞤮𞤲𞤺𞤢𞤲𞤪𞤫",
            "tok": "𞤚𞤮𞤳𞤭 𞤆𞤮𞤲𞤢𞥄𞤪𞤫",
            "tpi": "𞤚𞤮𞤳 𞤆𞤭𞤧𞤭𞤲𞤪𞤫",
            "tr": "𞤚𞤵𞥅𞤪𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "trv": "𞤚𞤢𞤪𞤮𞤳𞤮𞥅𞤪𞤫",
            "ts": "𞤚𞤭𞤧𞤮𞤲𞤺𞤢𞥄𞤪𞤫",
            "tt": "𞤚𞤢𞤼𞤢𞤪𞥇𞤪𞤫",
            "ttm": "𞤚𞤵𞤼𞤷𞤮𞤲𞤫𞤲𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "tum": "𞤚𞤵𞤥𞤦𞤵𞤳𞤢𞥄𞤪𞤫",
            "tvl": "𞤚𞤵𞤾𞤢𞤤𞤵𞥅𞤪𞤫",
            "twq": "𞤚𞤢𞤧𞤢𞥄𞤹𞤪𞤫",
            "ty": "𞤚𞤢𞤸𞤭𞤼𞤭𞤴𞤢𞤲𞤪𞤫",
            "tyv": "𞤚𞤵𞤾𞤭𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "tzm": "𞤚𞤢𞤥𞤢𞤶𞤭𞤼𞤪𞤫 𞤅𞤢𞤲𞤼𞤪𞤢𞤤 𞤀𞤼𞤤𞤢𞤧",
            "udm": "𞤓𞤣𞤥𞤵𞤪𞤼𞤪𞤫",
            "ug": "𞤓𞥅𞤴𞤺𞤵𞥅𞤪𞤫",
            "uk": "𞤒𞤵𞤳𞤪𞤫𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "umb": "𞤓𞤥𞤦𞤵𞤲𞤣𞤵𞥅𞤪𞤫",
            "und": "𞤍𞤫𞤲𞤺𞤢𞤤 𞤢𞤧-𞤢𞤲𞤣𞤢𞥄𞤲𞤺𞤢𞤤",
            "ur": "𞤓𞤪𞤣𞤵𞥅𞤪𞤫",
            "uz": "𞤓𞥅𞤧𞤦𞤫𞤳𞤪𞤫",
            "vai": "𞤾𞤢𞥄𞤴𞤪𞤫",
            "ve": "𞤏𞤫𞤲𞤣𞤢𞥄𞤪𞤫",
            "vec": "𞤏𞤫𞤲𞤭𞥅𞤧𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "vi": "𞤏𞤭𞤴𞤫𞤼𞤲𞤢𞤥𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "vo": "𞤏𞤮𞤤𞤢𞤨𞤵𞤳𞤪𞤫",
            "vun": "𞤏𞤵𞤲𞤶𞤮𞥅𞤪𞤫",
            "wa": "𞤏𞤢𞥄𞤤𞤮𞤲𞤳𞤮𞥅𞤪𞤫",
            "wae": "𞤏𞤢𞤤𞤧𞤫𞥅𞤪𞤫",
            "wal": "𞤏𞤮𞥅𞤤𞤢𞤴𞤼𞤢𞥄𞤪𞤫",
            "war": "𞤏𞤢𞤪𞤢𞤴𞤫𞥅𞤪𞤫",
            "wo": "𞤏𞤮𞤤𞤮𞤬𞤪𞤫",
            "wuu": "𞤏𞤵𞥅𞤪𞤫 𞤅𞤭𞥅𞤲",
            "xal": "𞤑𞤢𞤤𞤥𞤵𞤳𞤪𞤫",
            "xh": "𞤑𞤮𞥅𞤧𞤢𞥄𞤪𞤫",
            "xog": "𞤅𞤮𞤺𞤢𞥄𞤪𞤫",
            "yav": "𞤒𞤢𞤲𞤺𞤦𞤫𞥅𞤪𞤫",
            "ybb": "𞤒𞤫𞤥𞤦𞤢𞥄𞤪𞤫",
            "yi": "𞤒𞤭𞤣𞤭𞤧𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "yo": "𞤒𞤮𞥅𞤪𞤵𞤦𞤢𞥄𞤪𞤫",
            "yrl": "𞤛𞤫𞥅𞤲𞤺𞤢𞤼𞤵𞥅𞤪𞤫",
            "yue": "𞤑𞤢𞤲𞤼𞤮𞤲𞤫𞥅𞤪𞤫",
            "zgh": "𞤚𞤢𞤥𞤢𞥁𞤭𞤼𞤪𞤫 𞤖𞤢𞤲𞤼𞤵𞤲𞥋𞤣𞤫 𞤃𞤢𞤪𞤮𞥅𞤳",
            "zh": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "zh-Hans": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤖𞤮𞤴𞤬𞤭𞤲𞤢𞥄𞤲𞤣𞤫",
            "zh-Hant": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤒𞤫𞤷𞥆𞤵𞤲𞥋𞤣𞤫",
            "zu": "𞥁𞤵𞤤𞤵𞥅𞤪𞤫",
            "zun": "𞤟𞤵𞤲𞤭𞥅𞤪𞤫",
            "zxx": "𞤀𞤤𞤢𞥄 𞤦𞤢𞤯𞤮𞤪𞤢𞤤 𞤯𞤫𞤲𞤯𞤢𞤲𞤳𞤮",
            "zza": "𞤟𞤢𞥁𞤢𞥄𞤪𞤫"
          },
          "narrow": {
          },
          "short": {
            "az": "𞤀𞤶𞤢𞤪𞤭𞥅𞤪𞤫",
            "en-GB": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤁𞤘",
            "en-US": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 𞤁𞤂𞤀"
          }
        },
        "standard": {
          "long": {
            "aa": "𞤀𞤬𞤢𞥄𞤪𞤫",
            "ab": "𞤀𞤦𞤳𞤢𞥄𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ace": "𞤀𞥄𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ada": "𞤀𞤣𞤢𞤲𞤺𞤥𞤫𞥅𞤪𞤫",
            "ady": "𞤀𞤣𞤭𞤿𞤭𞥅𞤪𞤫",
            "af": "𞤀𞤬𞤪𞤭𞤳𞤢𞤲𞤪𞤫",
            "agq": "𞤀𞤺𞤸𞤫𞤥𞤪𞤫",
            "ain": "𞤀𞤴𞤲𞤵𞥅𞤪𞤫",
            "ak": "𞤀𞤳𞤢𞤲𞤪𞤫",
            "ale": "𞤀𞤤𞤫𞤵𞤼𞤵𞥅𞤪𞤫",
            "alt": "𞤀𞤤𞤼𞤢𞤴𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮𞥅𞤪𞤫",
            "am": "𞤀𞤥𞤸𞤢𞤪𞤭𞥅𞤪𞤫",
            "an": "𞤀𞤪𞤢𞤺𞤮𞤲𞤪𞤫",
            "ann": "𞤌𞤦𞤮𞤤𞤮𞥅𞤪𞤫",
            "anp": "𞤀𞤲𞤺𞤭𞤳𞤢𞥄𞤪𞤫",
            "ar": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫",
            "ar-001": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫 (𞤀𞤣𞤵𞤲𞤢)",
            "arn": "𞤃𞤢𞤨𞤵𞤷𞤭𞥅𞤪𞤫",
            "arp": "𞤀𞤪𞤢𞤨𞤢𞤸𞤮𞥅𞤪𞤫",
            "ars": "𞤀𞥄𞤪𞤢𞤦𞤫𞥅𞤪𞤫 𞤐𞤢𞤶𞤣𞤭",
            "as": "𞤀𞤧𞤢𞤥𞤫𞥅𞤪𞤫",
            "asa": "𞤀𞤧𞤵𞥅𞤪𞤫",
            "ast": "𞤀𞤧𞤼𞤵𞤪𞤭𞥅𞤪𞤫",
            "atj": "𞤀𞤼𞤭𞤥𞤫𞤳𞤵𞤱𞤪𞤫",
            "av": "𞤀𞤬𞤱𞤢𞤪𞤭𞥅𞤪𞤫",
            "awa": "𞤀𞤱𞤢𞤣𞤭𞥅𞤪𞤫",
            "ay": "𞤀𞤴𞤥𞤢𞤪𞤢𞥄𞤪𞤫",
            "az": "𞤀𞤶𞤢𞤪𞤦𞤢𞤴𞤭𞤶𞤢𞤲𞤭𞥅𞤪𞤫",
            "ba": "𞤄𞤢𞤧𞤳𞤭𞥅𞤪𞤫",
            "ban": "𞤄𞤢𞥄𞤤𞤭𞥅𞤪𞤫",
            "bas": "𞤄𞤢𞤧𞤢𞥄𞤪𞤫",
            "be": "𞤄𞤫𞤤𞤢𞤪𞤭𞥅𞤧𞤭𞥅𞤪𞤫",
            "bem": "𞤄𞤫𞤥𞤦𞤢𞥄𞤪𞤫",
            "bez": "𞤄𞤫𞤲𞤢𞥄𞤪𞤫",
            "bg": "𞤄𞤭𞤤𞤺𞤢𞥄𞤪𞤫",
            "bho": "𞤄𞤮𞤧𞤨𞤵𞤪𞤭𞥅𞤪𞤫",
            "bi": "𞤄𞤭𞤧𞤤𞤢𞤥𞤢𞥄𞤪𞤫",
            "bin": "𞤄𞤭𞤲𞤭𞥅𞤪𞤫",
            "bla": "𞤅𞤭𞤳𞤧𞤭𞤳𞤢𞥄𞤪𞤫",
            "bm": "𞤄𞤢𞤥𞤦𞤢𞤪𞤢𞥄𞤪𞤫",
            "bn": "𞤄𞤫𞤲𞤺𞤢𞤤𞤭𞥅𞤪𞤫",
            "bo": "𞤚𞤭𞤦𞤫𞤼𞤫𞤲𞤪𞤫",
            "br": "𞤄𞤫𞤪𞤫𞤼𞤮𞤲𞤪𞤫",
            "brx": "𞤄𞤮𞤣𞤮𞥅𞤪𞤫",
            "bs": "𞤄𞤮𞤧𞤲𞤭𞤴𞤢𞥄𞤪𞤫",
            "bug": "𞤄𞤵𞤺𞤭𞤧𞤢𞥄𞤪𞤫",
            "byn": "𞤄𞤭𞤤𞤭𞤲𞤪𞤫",
            "ca": "𞤑𞤢𞤼𞤢𞤤𞤢𞤲𞤪𞤫",
            "cay": "𞤑𞤢𞤴𞤺𞤢𞥄𞤪𞤫",
            "ccp": "𞤅𞤢𞤳𞤥𞤢𞥄𞤪𞤫",
            "ce": "𞤕𞤫𞤷𞤫𞤲𞤪𞤫",
            "ceb": "𞤅𞤫𞤦𞤱𞤢𞤲𞤮𞥅𞤪𞤫",
            "cgg": "𞤕𞤭𞤺𞤢𞥄𞤪𞤫",
            "ch": "𞤕𞤢𞤥𞤮𞤪𞤮𞥅𞤪𞤫",
            "chk": "𞤕𞤵𞥅𞤳𞤵𞥅𞤪𞤫",
            "chm": "𞤃𞤢𞤪𞤭𞥅𞤪𞤫",
            "cho": "𞤕𞤢𞤸𞤼𞤢𞥄𞤪𞤫",
            "chp": "𞤕𞤭𞤨𞤴𞤢𞤲𞤪𞤫",
            "chr": "𞤕𞤫𞥅𞤪𞤮𞤳𞤭𞥅𞤪𞤫",
            "chy": "𞤅𞤢𞥄𞤴𞤢𞤲𞤪𞤫",
            "ckb": "𞤑𞤵𞤪𞤣𞤵𞥅𞤪𞤫",
            "clc": "𞤕𞤭𞤤𞤳𞤮𞤼𞤭𞤲𞤪𞤫",
            "co": "𞤑𞤮𞤪𞤧𞤭𞤳𞤢𞥄𞤪𞤫",
            "crg": "𞤃𞤭𞤷𞤭𞤬𞤪𞤫",
            "crj": "𞤑𞤪𞤭𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "crk": "𞤆𞤤𞤫𞤭𞤲𞤧 𞤑𞤪𞤭𞥅𞤪𞤫",
            "crl": "Vote 𞤑𞤪𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞤬𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "crm": "𞤃𞤮𞥅𞤧𞤫 𞤑𞤪𞤭𞥅𞤪𞤫",
            "crr": "𞤀𞤤𞤺𞤮𞤲𞤳𞤭𞤲𞤪𞤫 𞤑𞤢𞥄𞤪𞤤𞤭𞤲𞤢",
            "cs": "𞤕𞤫𞤳𞤧𞤭𞤲𞤢𞥄𞤪𞤫",
            "csw": "𞤑𞤪𞤭𞥅𞤪𞤫 𞤅𞤢𞤱𞤨𞤭𞥅",
            "cu": "𞤅𞤭𞤤𞤾𞤭𞤳𞤪𞤫 𞤕𞤮𞥅𞤷𞤭",
            "cv": "𞤕𞤵𞥅𞤾𞤢𞤧𞤪𞤫",
            "cy": "𞤘𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "da": "𞤁𞤢𞥄𞤲𞤭𞤧𞤳𞤮𞥅𞤪𞤫",
            "dak": "𞤁𞤢𞤳𞤮𞤼𞤢𞥄𞤪𞤫",
            "dar": "𞤁𞤢𞤪𞤺𞤭𞤲𞤢𞥄𞤪𞤫",
            "dav": "𞤚𞤢𞤭𞤼𞤢𞥄𞤪𞤫",
            "de": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "de-AT": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫 (𞤌𞤼𞤭𞤪𞤧𞤢)",
            "de-CH": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫 (𞤅𞤵𞤱𞤭𞤪𞤧𞤢𞥄)",
            "dgr": "𞤁𞤮𞤺𞤪𞤭𞤦𞤪𞤫",
            "dje": "𞤔𞤢𞤪𞤥𞤢𞥄𞤪𞤫",
            "doi": "𞤁𞤮𞤺𞤪𞤭𞥅𞤪𞤫",
            "dsb": "𞤂𞤫𞤧 𞤅𞤮𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫",
            "dua": "𞤁𞤵𞤱𞤢𞤤𞤢𞥄𞤪𞤫",
            "dv": "𞤁𞤭𞥅𞤬𞤫𞤸𞤭𞥅𞤪𞤫",
            "dyo": "𞤔𞤮𞥅𞤤𞤢𞥄𞤪𞤫",
            "dz": "𞤄𞤵𞥅𞤼𞤢𞤲𞤪𞤫",
            "dzg": "𞤁𞤢𞤶𞤢𞤺𞤢𞥄𞤪𞤫",
            "ebu": "𞤉𞤥𞤦𞤵𞥅𞤪𞤫",
            "ee": "𞤉𞤱𞤫𞥅𞤪𞤫",
            "efi": "𞤉𞤬𞤭𞤳𞤪𞤫",
            "eka": "𞤉𞤳𞤢𞤶𞤵𞤳𞤪𞤫",
            "el": "𞤘𞤭𞥅𞤪𞤧𞤢𞥄𞤪𞤫",
            "en": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫",
            "en-AU": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄)",
            "en-CA": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤑𞤢𞤲𞤢𞤣𞤢𞥄)",
            "en-GB": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤁𞤫𞤲𞤼𞤢𞤤 𞤐𞤺𞤫𞤯𞤵𞥅𞤪𞤭)",
            "en-US": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤁𞤫𞤲𞤼𞤢𞤤 𞤂𞤢𞤪𞤫)",
            "eo": "𞤉𞤧𞤨𞤫𞤪𞤢𞤲𞤼𞤮𞥅𞤪𞤫",
            "es": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "es-419": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 (𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 𞤂𞤢𞤼𞤭𞤲𞤳𞤮)",
            "es-ES": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 (𞤉𞤧𞤨𞤢𞤻𞤢𞥄)",
            "es-MX": "𞤅𞤭𞤨𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 (𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞥅)",
            "et": "𞤉𞤧𞤼𞤮𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "eu": "𞤄𞤢𞤧𞤳𞤢𞤪𞤢𞥄𞤪𞤫",
            "ewo": "𞤉𞤱𞤮𞤲𞤣𞤮𞥅𞤪𞤫",
            "fa": "𞤊𞤢𞥄𞤪𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "fa-AF": "𞤊𞤢𞥄𞤪𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 (𞤀𞤬𞤺𞤢𞤲𞤭𞤧𞤼𞤢𞥄𞤲)",
            "ff": "𞤆𞤵𞤤𞤢𞤪",
            "fi": "𞤊𞤫𞤲𞤭𞤧𞤪𞤫",
            "fil": "𞤊𞤭𞤤𞤭𞤨𞤭𞤲𞤮𞥅𞤪𞤫",
            "fj": "𞤊𞤭𞥅𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "fo": "𞤊𞤫𞤪𞤮𞤱𞤫𞤧𞤪𞤫",
            "fon": "𞤊𞤮𞤲𞤪𞤫",
            "fr": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫",
            "fr-CA": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 (𞤑𞤢𞤲𞤢𞤣𞤢𞥄)",
            "fr-CH": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 (𞤅𞤵𞤱𞤭𞤪𞤧𞤢𞥄)",
            "frc": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭𞥅𞤪𞤫 𞤑𞤢𞤣𞤭𞤴𞤫𞤲𞤪𞤫",
            "fur": "𞤊𞤭𞤪𞥇𞤵𞤤𞤭𞤴𞤢𞤲𞤪𞤫",
            "fy": "𞤊𞤭𞤪𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤖𞤭𞤪𞤲𞤢",
            "ga": "𞤋𞤪𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "gaa": "𞤘𞤢𞥄𞤪𞤫",
            "gd": "𞤅𞤭𞤳𞤮𞤼𞤭𞤧𞤪𞤫 𞤘𞤢𞤫𞤭𞤳",
            "gez": "𞤘𞤫𞥅𞤶𞤪𞤫",
            "gil": "𞤘𞤭𞤤𞤦𞤫𞤪𞤼𞤫𞥅𞤧𞤪𞤫",
            "gl": "𞤘𞤢𞤤𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "gn": "𞤘𞤵𞤢𞤪𞤢𞤲𞤭𞥅𞤪𞤫",
            "gor": "𞤘𞤮𞤪𞤮𞤲𞤼𞤢𞤤𞤮𞥅𞤪𞤫",
            "gsw": "𞤔𞤫𞤪𞤥𞤢𞤲𞤪𞤫 𞤅𞤵𞤱𞤭𞤧",
            "gu": "𞤘𞤵𞤶𞤢𞤪𞤢𞤼𞤭𞥅𞤪𞤫",
            "guz": "𞤘𞤵𞤧𞤭𞥅𞤪𞤫",
            "gv": "𞤃𞤢𞤲𞤳𞤭𞤧𞤪𞤫",
            "gwi": "𞤘𞤭𞤱𞤧𞤭𞤲𞤪𞤫",
            "ha": "𞤖𞤢𞤱𞤧𞤢𞥄𞤪𞤫",
            "hai": "𞤖𞤢𞤴𞤣𞤢𞥄𞤪𞤫",
            "haw": "𞤖𞤢𞤱𞤢𞥄𞤭𞤴𞤫𞤲𞤪𞤫",
            "hax": "𞤖𞤢𞤭𞤣𞤢𞥄𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "he": "𞤖𞤭𞤦𞤵𞤪𞤵𞥅𞤪𞤫",
            "hi": "𞤖𞤭𞤲𞤣𞤭𞥅𞤪𞤫",
            "hil": "𞤖𞤭𞤤𞤭𞤺𞤢𞤴𞤲𞤮𞤲𞤪𞤫",
            "hmn": "𞤖𞤵𞤥𞤺𞤵𞤲𞤪𞤫",
            "hr": "𞤑𞤮𞤪𞤮𞤱𞤢𞤧𞤭𞥅𞤪𞤫",
            "hsb": "𞤅𞤮𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫 𞤁𞤮𞤱𞤪𞤭",
            "ht": "𞤀𞤳𞤵𞥅𞤪𞤫 𞤖𞤢𞤴𞤼𞤭𞥅",
            "hu": "𞤖𞤵𞤲𞤺𞤢𞤪𞤭𞤴𞤢𞤲𞤪𞤫",
            "hup": "𞤖𞤵𞤨𞤢𞥄𞤪𞤫",
            "hur": "𞤖𞤢𞤤𞤳𞤮𞤥𞤫𞤤𞤫𞤥𞤪𞤫",
            "hy": "𞤀𞤪𞤥𞤫𞤲𞤭𞥅𞤪𞤫",
            "hz": "𞤖𞤫𞤪𞤫𞤪𞤮𞥅𞤪𞤫",
            "ia": "𞤉𞤲𞤼𞤫𞤪𞤤𞤭𞤺𞤢𞥄𞤪𞤫",
            "iba": "𞤋𞤦𞤢𞤲𞤪𞤫",
            "ibb": "𞤋𞤦𞤭𞥅𞤦𞤭𞤴𞤮𞥅𞤪𞤫",
            "id": "𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "ig": "𞤋𞤦𞤮𞥅𞤪𞤫",
            "ii": "𞤅𞤭𞤧𞤵𞤢𞤲𞤪𞤫 𞤒𞤭𞥅",
            "ikt": "𞤋𞤲𞤵𞤳𞤼𞤵𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤑𞤢𞤲𞤢𞤣𞤢𞥄",
            "ilo": "𞤋𞤤𞤮𞤳𞤮𞥅𞤪𞤫",
            "inh": "𞤋𞤲𞤺𞤮𞤧𞤫𞥅𞤪𞤫",
            "io": "𞤋𞤣𞤮𞥅𞤪𞤫",
            "is": "𞤀𞤴𞤧𞤭𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "it": "𞤋𞤼𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "iu": "𞤋𞤲𞤵𞤳𞤼𞤫𞥅𞤪𞤫",
            "ja": "𞤐𞤭𞤨𞤮𞤲𞤪𞤫",
            "jbo": "𞤂𞤮𞤶𞤦𞤢𞤲𞤪𞤫",
            "jgo": "𞤐𞤺𞤮𞤥𞤦𞤢𞥄𞤪𞤫",
            "jmc": "𞤃𞤢𞤳𞤢𞤥𞤫𞥅𞤪𞤫",
            "jv": "𞤔𞤢𞥄𞤱𞤢𞤫𞥅𞤪𞤫",
            "ka": "𞤔𞤮𞥅𞤪𞥁𞤭𞤴𞤢𞤲𞤪𞤫",
            "kab": "𞤑𞤢𞤦𞤭𞤤𞤭𞥅𞤪𞤫",
            "kac": "𞤑𞤢𞤧𞤭𞤲𞤪𞤫",
            "kaj": "𞤑𞤢𞤶𞤫𞥅𞤪𞤫",
            "kam": "𞤑𞤢𞤥𞤦𞤢𞥄𞤪𞤫",
            "kbd": "𞤑𞤢𞤦𞤢𞤪𞤣𞤭𞤴𞤢𞤲𞤪𞤫",
            "kcg": "𞤚𞤵𞤴𞤢𞤨𞤵𞥅𞤪𞤫",
            "kde": "𞤃𞤢𞤳𞤮𞤲𞤣𞤫𞥅𞤪𞤫",
            "kea": "𞤑𞤢𞤦𞤵𞤾𞤫𞤪𞤣𞤭𞤴𞤢𞤲𞤪𞤫",
            "kfo": "𞤑𞤮𞤪𞤮𞥅𞤪𞤫",
            "kgp": "𞤑𞤢𞤭𞤲𞤺𞤢𞤲𞤺𞤪𞤫",
            "kha": "𞤝𞤢𞤧𞤭𞥅𞤪𞤫",
            "khq": "𞤑𞤮𞤴𞤪𞤢𞤷𞤭𞤲𞤪𞤫",
            "ki": "𞤑𞤭𞤳𞤵𞤴𞤵𞥅𞤪𞤫",
            "kj": "𞤑𞤵𞤢𞤻𞤢𞤥𞤢𞥄𞤪𞤫",
            "kk": "𞤑𞤢𞥁𞤢𞤳𞤪𞤫",
            "kkj": "𞤑𞤢𞤳𞤮𞥅𞤪𞤫",
            "kl": "𞤑𞤢𞤤𞤢𞥄𞤤𞤧𞤵𞤼𞤪𞤫",
            "kln": "𞤑𞤢𞤤𞤫𞤲𞤶𞤭𞤲𞤪𞤫",
            "km": "𞤑𞤵𞤥𞤢𞤴𞤪𞤫",
            "kmb": "𞤑𞤭𞤥𞤦𞤵𞤲𞤣𞤵𞥅𞤪𞤫",
            "kn": "𞤑𞤢𞤲𞥆𞤢𞤣𞤢𞥄𞤪𞤫",
            "ko": "𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞤲𞤪𞤫",
            "kok": "𞤑𞤮𞤲𞤳𞤢𞤲𞤭𞥅𞤪𞤫",
            "kpe": "𞤘𞤫𞤪𞤧𞤫𞥅𞤪𞤫",
            "kr": "𞤑𞤮𞥅𞤤𞤫𞥅𞤪𞤫",
            "krc": "𞤑𞤢𞤪𞤢𞤧𞤢𞤴-𞤄𞤢𞤤𞤳𞤢𞥄𞤪𞤫",
            "krl": "𞤑𞤢𞤪𞤫𞤤𞤭𞤢𞤲𞤪𞤫",
            "kru": "𞤑𞤵𞤪𞤵𞤿𞤵𞥅𞤪𞤫",
            "ks": "𞤑𞤢𞥃𞤥𞤭𞥅𞤪𞤫",
            "ksb": "𞤅𞤢𞤥𞤦𞤢𞤤𞤢𞥄𞤪𞤫",
            "ksf": "𞤄𞤢𞤬𞤭𞤴𞤢𞥄𞤪𞤫",
            "ksh": "𞤑𞤮𞤤𞤮𞤺𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "ku": "𞤑𞤵𞤪𞤣𞤭𞤧𞤭𞥅𞤪𞤫",
            "kum": "𞤑𞤵𞤥𞤴𞤢𞤳𞤪𞤫",
            "kv": "𞤑𞤮𞤥𞤭𞥅𞤪𞤫",
            "kw": "𞤑𞤮𞤪𞤲𞤭𞥅𞤪𞤫",
            "kwk": "𞤑𞤢𞤱𞤳𞥇𞤱𞤢𞤤𞤢𞥄𞤪𞤫",
            "ky": "𞤑𞤭𞤪𞤺𞤵𞥅𞤪𞤫",
            "la": "𞤂𞤢𞤼𞤫𞤲𞤪𞤫",
            "lad": "𞤂𞤢𞤣𞤭𞤲𞤮𞥅𞤪𞤫",
            "lag": "𞤂𞤢𞤲𞤺𞤭𞥅𞤪𞤫",
            "lb": "𞤂𞤵𞥁𞤫𞤲𞤦𞤵𞥅𞤪𞤺𞤭𞤧𞤪𞤫",
            "lez": "𞤂𞤫𞥁𞤺𞤭𞤴𞤢𞤲𞤪𞤫",
            "lg": "𞤘𞤢𞤲𞤣𞤢𞥄𞤪𞤫",
            "li": "𞤂𞤭𞤥𞤦𞤵𞤪𞤺𞤵𞤧𞤪𞤫",
            "lij": "𞤂𞤳𞤭𞤺𞤵𞥅𞤪𞤫",
            "lil": "𞤂𞤭𞤤𞥆𞤮𞥅𞤫𞤼𞤪𞤫",
            "lkt": "𞤂𞤢𞤳𞤮𞤼𞤢𞥄𞤪𞤫",
            "ln": "𞤂𞤭𞤲𞤺𞤢𞤤𞤢𞥄𞤪𞤫",
            "lo": "𞤂𞤢𞤮𞥅𞤪𞤫",
            "lou": "𞤀𞤳𞤵𞥅𞤪𞤫 𞤂𞤵𞥅𞥁𞤭𞤴𞤢𞥄𞤲𞤢",
            "loz": "𞤂𞤮𞥁𞤭𞥅𞤪𞤫",
            "lrc": "𞤂𞤵𞤪𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "lsm": "𞤅𞤢𞥄𞤥𞤭𞤢𞥄𞤪𞤫",
            "lt": "𞤂𞤭𞤼𞤮𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "lu": "𞤂𞤵𞤦𞤢-𞤑𞤢𞤼𞤢𞤲𞤺𞤢𞥄𞤪𞤫",
            "lua": "𞤂𞤵𞤦𞤢 𞤑𞤢𞤧𞤢𞤭𞤪𞤫",
            "lun": "𞤂𞤵𞤲𞤣𞤢𞥄𞤪𞤫",
            "luo": "𞤂𞤵𞤮𞥅𞤪𞤫",
            "lus": "𞤃𞤭𞤧𞤮𞥅𞤪𞤫",
            "luy": "𞤂𞤵𞤴𞤭𞤢𞥄𞤪𞤫",
            "lv": "𞤂𞤢𞤼𞤾𞤭𞤴𞤢𞤲𞤪𞤫",
            "mad": "𞤃𞤢𞤣𞤵𞤪𞤫𞥅𞤪𞤫",
            "mag": "𞤃𞤢𞤺𞤢𞤸𞤭𞥅𞤪𞤫",
            "mai": "𞤃𞤢𞤴𞤭𞤼𞤭𞤤𞤭𞥅𞤪𞤫",
            "mak": "𞤃𞤢𞤳𞤢𞤧𞤢𞤪𞤢𞥄𞤪𞤫",
            "mas": "𞤃𞤢𞤧𞤢𞤴𞤭𞥅𞤪𞤫",
            "mdf": "𞤃𞤮𞤳𞤧𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "men": "𞤃𞤫𞤲𞤣𞤫𞥅𞤪𞤫",
            "mer": "𞤃𞤫𞤪𞤵𞥅𞤪𞤫",
            "mfe": "𞤃𞤮𞤪𞤭𞥅𞤧𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mg": "𞤃𞤢𞤤𞤢𞤺𞤢𞤧𞤭𞥅𞤪𞤫",
            "mgh": "𞤃𞤢𞤳𞤵𞤱𞤢𞥄𞤪𞤫",
            "mgo": "𞤃𞤫𞤼𞤢𞥄𞤪𞤫",
            "mh": "𞤃𞤢𞤪𞤧𞤢𞤤𞤫𞥅𞤪𞤫",
            "mi": "𞤃𞤢𞥄𞤮𞤪𞤭𞥅𞤪𞤫",
            "mic": "𞤃𞤭𞤳𞤥𞤢𞤹𞤵𞥅𞤪𞤫",
            "min": "𞤃𞤭𞤲𞤢𞤲𞤺𞤳𞤢𞤦𞤢𞤵𞥅𞤪𞤫",
            "mk": "𞤃𞤢𞤧𞤫𞤣𞤮𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "ml": "𞤃𞤢𞤤𞤢𞤴𞤢𞤤𞤢𞤥𞤪𞤫",
            "mn": "𞤃𞤮𞤲𞤺𞤮𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mni": "𞤃𞤢𞤲𞤭𞤨𞤵𞥅𞤪𞤫",
            "moe": "𞤋𞤲𞥆𞤵-𞤢𞤴𞤥𞤵𞤲𞤪𞤫",
            "moh": "𞤃𞤮𞥅𞤸𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "mos": "𞤃𞤮𞥅𞤧𞤭𞥅𞤪𞤫",
            "mr": "𞤃𞤢𞤪𞤢𞤼𞤭𞥅𞤪𞤫",
            "ms": "𞤃𞤢𞤤𞤫𞥅𞤪𞤫",
            "mt": "𞤃𞤢𞤤𞤼𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "mua": "𞤃𞤵𞤲𞤣𞤢𞤲𞤪𞤫",
            "mul": "𞤍𞤫𞤲𞤯𞤫 𞤅𞤫𞤪𞤼𞤵𞤯𞤫",
            "mus": "𞤃𞤵𞤧𞤳𞤮𞤳𞤭𞥅𞤪𞤫",
            "mwl": "𞤃𞤭𞤪𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "my": "𞤄𞤵𞤪𞤥𞤢𞥄𞤪𞤫",
            "myv": "𞤉𞤪𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "mzn": "𞤃𞤢𞥁𞤢𞤲𞤣𞤫𞤪𞤢𞤲𞤭𞥅𞤪𞤫",
            "na": "𞤐𞤢𞤱𞤵𞤪𞤵𞤲𞤳𞤮𞥅𞤪𞤫",
            "nap": "𞤐𞤢𞥄𞤨𞤮𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "naq": "𞤐𞤢𞤥𞤢𞥄𞤪𞤫",
            "nb": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫 𞤄𞤮𞤳𞤥𞤢𞤤",
            "nd": "𞤐𞤣𞤫𞤦𞤫𞤤𞤫𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤺𞤫",
            "nds": "𞤂𞤫𞤧-𞤀𞤤𞤵𞤥𞤢𞤲𞤪𞤫",
            "nds-NL": "𞤂𞤫𞤧-𞤀𞤤𞤵𞤥𞤢𞤲𞤪𞤫 (𞤖𞤮𞤤𞤢𞤲𞤣𞤭𞤴𞤢𞥄)",
            "ne": "𞤐𞤫𞤨𞤢𞤤𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "new": "𞤐𞤫𞤱𞤢𞤪𞤭𞥅𞤪𞤫",
            "ng": "𞤐𞤣𞤮𞤲𞤺𞤢𞥄𞤪𞤫",
            "nia": "𞤙𞤢𞤧𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "niu": "𞤐𞤭𞤵𞤫𞤴𞤢𞤲𞤪𞤫",
            "nl": "𞤁𞤮𞥅𞤷𞤵𞤪𞤫",
            "nl-BE": "𞤁𞤮𞥅𞤷𞤵𞤪𞤫 (𞤄𞤫𞤤𞤶𞤭𞤳𞤢𞥄)",
            "nmg": "𞤐𞤺𞤵𞤥𞤦𞤢𞥄𞤪𞤫",
            "nn": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫 𞤙𞤮𞤪𞤧𞤳",
            "nnh": "𞤐𞤶𞤢𞤥𞤦𞤵𞥅𞤪𞤫",
            "no": "𞤐𞤮𞤪𞤱𞤫𞤶𞤭𞤴𞤢𞤲𞤪𞤫",
            "nog": "𞤐𞤮𞤺𞤢𞤭𞥅𞤪𞤫",
            "nqo": "𞤐𞤳𞤮𞥅𞤪𞤫",
            "nr": "𞤐𞤣𞤫𞤦𞤫𞤤𞤫𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫",
            "nso": "𞤅𞤮𞤼𞤮𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "nus": "𞤐𞤵𞤫𞤪𞤭𞥅𞤪𞤫",
            "nv": "𞤐𞤢𞤬𞤱𞤢𞤸𞤮𞥅𞤪𞤫",
            "ny": "𞤙𞤢𞤲𞤶𞤢𞥄𞤪𞤫",
            "nyn": "𞤙𞤢𞤲𞤳𞤮𞤤𞤫𞥅𞤪𞤫",
            "oc": "𞤌𞤷𞥆𞤭𞤼𞤢𞤲𞤪𞤫",
            "ojb": "𞤌𞤶𞤭𞤦𞤵𞤱𞤢𞥄𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "ojc": "𞤌𞤶𞤭𞤦𞤵𞤱𞤪𞤫 𞤕𞤢𞤳𞤢",
            "ojs": "𞤌𞤶𞤭-𞤑𞤪𞤭𞥅𞤪𞤫",
            "ojw": "𞤌𞤶𞤭𞤦𞤱𞤢𞥄𞤪𞤫 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫",
            "oka": "𞤌𞤳𞤢𞤲𞤢𞤺𞤢𞤲𞤪𞤫",
            "om": "𞤌𞤪𞤮𞤥𞤮𞥅𞤪𞤫",
            "or": "𞤌𞤣𞤭𞤢𞥄𞤪𞤫",
            "os": "𞤌𞤧𞥆𞤫𞤼𞤭𞤳𞤪𞤫",
            "pa": "𞤆𞤵𞤲𞤶𞤢𞥄𞤦𞤭𞥅𞤪𞤫",
            "pag": "𞤆𞤢𞤲𞤺𞤢𞤧𞤭𞤲𞤢𞤲𞤪𞤫",
            "pam": "𞤆𞤢𞤥𞤨𞤢𞤲𞤺𞤢𞥄𞤪𞤫",
            "pap": "𞤆𞤢𞤨𞤭𞤢𞤥𞤫𞤲𞤼𞤮𞥅𞤪𞤫",
            "pau": "𞤆𞤢𞤤𞤢𞤵𞤴𞤢𞤲𞤪𞤫",
            "pcm": "𞤆𞤭𞤶𞤫𞤲𞤪𞤫 𞤐𞤢𞤶𞤭𞤪𞤭𞤴𞤢𞥄",
            "pis": "𞤆𞤭𞤶𞤭𞤲𞤪𞤫",
            "pl": "𞤆𞤮𞤤𞤢𞤲𞤣𞤭𞥅𞤪𞤫",
            "pqm": "𞤃𞤢𞤤𞤭𞤧𞤫𞥅𞤼-𞤆𞤢𞤧𞤢𞤥𞤢𞤹𞤵𞤮𞤣𞥆𞤭",
            "prg": "𞤆𞤵𞤪𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "ps": "𞤆𞤢𞤧𞤼𞤵𞤲𞤪𞤫",
            "pt": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫",
            "pt-BR": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫 (𞤄𞤪𞤢𞤧𞤭𞤤)",
            "pt-PT": "𞤆𞤮𞤪𞤼𞤮𞤳𞤫𞥅𞤧𞤭𞥅𞤪𞤫 (𞤆𞤮𞥅𞤪𞤼𞤵𞤺𞤢𞥄𞤤)",
            "qu": "𞤗𞤵𞤷𞤵𞤢𞤲𞤪𞤫",
            "rap": "𞤈𞤢𞤨𞤢𞤲𞤵𞤭𞥅𞤪𞤫",
            "rar": "𞤈𞤢𞤪𞤮𞤼𞤮𞤲𞤺𞤢𞤲𞤪𞤫",
            "rhg": "𞤈𞤮𞤸𞤭𞤲𞤺𞤢𞥄𞤪𞤫",
            "rm": "𞤈𞤮𞤥𞤢𞤲𞤧𞤪𞤫",
            "rn": "𞤈𞤵𞤲𞤣𞤭𞥅𞤪𞤫",
            "ro": "𞤈𞤮𞤥𞤢𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "ro-MD": "𞤈𞤮𞤥𞤢𞤲𞤭𞤴𞤢𞤲𞤪𞤫 (𞤃𞤮𞤤𞤣𞤮𞤾𞤢𞥄)",
            "rof": "𞤈𞤮𞤥𞤦𞤮𞥅𞤪𞤫",
            "ru": "𞤈𞤮𞥅𞤧𞤭𞤴𞤢𞤲𞤪𞤫",
            "rup": "𞤀𞤪𞤮𞤥𞤢𞤲𞤭𞥅𞤪𞤫",
            "rw": "𞤑𞤭𞤻𞤭𞤪𞤵𞤱𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "rwk": "𞤈𞤵𞤱𞤢𞥄𞤪𞤫",
            "sa": "𞤅𞤢𞤲𞤧𞤳𞤪𞤭𞤼𞤪𞤫",
            "sad": "𞤅𞤢𞤲𞤣𞤢𞤱𞤫𞥅𞤪𞤫",
            "sah": "𞤅𞤢𞤿𞤢𞥄𞤪𞤫",
            "saq": "𞤅𞤢𞤥𞤦𞤵𞤪𞤵𞥅𞤪𞤫",
            "sat": "𞤅𞤢𞤲𞤼𞤢𞤤𞤭𞥅𞤪𞤫",
            "sba": "𞤐𞤺𞤢𞤥𞤦𞤢𞤴𞤪𞤫",
            "sbp": "𞤅𞤢𞤲𞤺𞤵𞥅𞤪𞤫",
            "sc": "𞤅𞤢𞤪𞤣𞤭𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "scn": "𞤅𞤭𞤧𞤭𞤤𞤭𞤴𞤢𞤲𞤪𞤫",
            "sco": "𞤅𞤭𞤳𞤮𞤼𞤧𞤪𞤫",
            "sd": "𞤅𞤭𞤲𞤣𞤢𞥄𞤪𞤫",
            "se": "𞤅𞤢𞤥𞤭𞥅𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "seh": "𞤅𞤫𞤲𞤢𞥄𞤪𞤫",
            "ses": "𞤑𞤮𞤪𞤮𞤦𞤮𞤪𞤮𞥅𞤪𞤫 𞤅𞤫𞤲𞥆𞤭",
            "sg": "𞤅𞤢𞤲𞤺𞤮𞥅𞤪𞤫",
            "shi": "𞤚𞤢𞤧𞤭𞤤𞤸𞤭𞤼𞤪𞤫",
            "shn": "𞤅𞤢𞤲𞤪𞤫",
            "si": "𞤅𞤭𞤲𞤸𞤢𞤤𞤢𞥄𞤪𞤫",
            "sk": "𞤅𞤤𞤮𞤾𞤢𞥄𞤳𞤪𞤫",
            "sl": "𞤅𞤤𞤮𞤾𞤫𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "slh": "𞤂𞤵𞥃𞤵𞤼𞤧𞤭𞤣𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "sm": "𞤅𞤢𞤥𞤮𞤢𞤲𞤪𞤫",
            "smn": "𞤋𞤲𞤢𞤪𞤭𞤧𞤳𞤢𞤤𞤭𞥅𞤪𞤫",
            "sms": "𞤅𞤭𞤳𞤮𞤤𞤼 𞤅𞤢𞤥𞤭𞥅𞤪𞤫",
            "sn": "𞤅𞤮𞤲𞤢𞥄𞤪𞤫",
            "snk": "𞤅𞤢𞤪𞤢𞤲𞤳𞤵𞤤𞥆𞤪𞤫",
            "so": "𞤅𞤮𞤥𞤢𞤤𞤭𞥅𞤪𞤫",
            "sq": "𞤀𞤤𞤦𞤢𞤲𞤭𞥅𞤪𞤫",
            "sr": "𞤅𞤫𞤪𞤦𞤭𞤴𞤢𞤲𞤪𞤫",
            "srn": "𞤅𞤢𞤪𞤲𞤢𞤲-𞤚𞤮𞤲𞤺𞤮𞥅𞤪𞤫",
            "ss": "𞤅𞤵𞤱𞤢𞤼𞤭𞥅𞤪𞤫",
            "st": "𞤅𞤮𞤼𞤮𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮",
            "str": "𞤅𞤭𞤼𞤪𞤭𞤼 𞤅𞤢𞤤𞤭𞤧𞤪𞤫",
            "su": "𞤅𞤵𞤲𞤣𞤢𞤲𞤭𞥅𞤪𞤫",
            "suk": "𞤅𞤵𞤳𞤵𞤥𞤢𞥄𞤪𞤫",
            "sv": "𞤅𞤱𞤫𞤣𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "sw": "𞤅𞤵𞤱𞤢𞤸𞤭𞤤𞤭𞥅𞤪𞤫",
            "sw-CD": "𞤅𞤵𞤱𞤢𞤸𞤭𞤤𞤭𞥅𞤪𞤫 (𞤑𞤮𞤲𞤺𞤮 - 𞤑𞤭𞤲𞤧𞤢𞤧𞤢)",
            "swb": "𞤑𞤮𞤥𞤮𞤪𞤭𞥅𞤪𞤫",
            "syr": "𞤅𞤭𞥅𞤪𞤭𞤴𞤢𞤳𞤪𞤫",
            "ta": "𞤚𞤢𞤥𞤵𞤤𞤪𞤫",
            "tce": "𞤚𞤵𞤼𞤳𞤮𞤲𞤭𞥅𞤪𞤫 𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫",
            "te": "𞤚𞤫𞤤𞤫𞤺𞤵𞥅𞤪𞤫",
            "tem": "𞤚𞤫𞤥𞤫𞤲𞤫𞥅𞤪𞤫",
            "teo": "𞤚𞤫𞤧𞤮𞥅𞤪𞤫",
            "tet": "𞤚𞤫𞤼𞤵𞤥𞤪𞤫",
            "tg": "𞤚𞤢𞤶𞤭𞤳𞤪𞤫",
            "tgx": "𞤚𞤢𞤺𞤭𞥃𞤪𞤫",
            "th": "𞤚𞤢𞤴𞤤𞤢𞤲𞤣𞤫𞥅𞤪𞤫",
            "tht": "𞤚𞤢𞤸𞤢𞤤𞤼𞤢𞤲𞤪𞤫",
            "ti": "𞤚𞤭𞤺𞤭𞤪𞤻𞤢𞥄𞤪𞤫",
            "tig": "𞤚𞤭𞤺𞤭𞤪𞤴𞤢𞤲𞤪𞤫",
            "tk": "𞤼𞤵𞤪𞤳𞤥𞤢𞤲𞤪𞤫",
            "tlh": "𞤑𞤭𞤤𞤭𞤲𞤺𞤮𞤲𞤪𞤫",
            "tli": "𞤚𞤤𞤭𞤲𞤺𞤭𞤼𞤪𞤫",
            "tn": "𞤚𞤭𞤧𞤱𞤢𞤲𞤢𞥄𞤪𞤫",
            "to": "𞤚𞤮𞤲𞤺𞤢𞤲𞤪𞤫",
            "tok": "𞤚𞤮𞤳𞤭 𞤆𞤮𞤲𞤢𞥄𞤪𞤫",
            "tpi": "𞤚𞤮𞤳 𞤆𞤭𞤧𞤭𞤲𞤪𞤫",
            "tr": "𞤚𞤵𞥅𞤪𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "trv": "𞤚𞤢𞤪𞤮𞤳𞤮𞥅𞤪𞤫",
            "ts": "𞤚𞤭𞤧𞤮𞤲𞤺𞤢𞥄𞤪𞤫",
            "tt": "𞤚𞤢𞤼𞤢𞤪𞥇𞤪𞤫",
            "ttm": "𞤚𞤵𞤼𞤷𞤮𞤲𞤫𞤲𞤪𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤫",
            "tum": "𞤚𞤵𞤥𞤦𞤵𞤳𞤢𞥄𞤪𞤫",
            "tvl": "𞤚𞤵𞤾𞤢𞤤𞤵𞥅𞤪𞤫",
            "twq": "𞤚𞤢𞤧𞤢𞥄𞤹𞤪𞤫",
            "ty": "𞤚𞤢𞤸𞤭𞤼𞤭𞤴𞤢𞤲𞤪𞤫",
            "tyv": "𞤚𞤵𞤾𞤭𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "tzm": "𞤚𞤢𞤥𞤢𞤶𞤭𞤼𞤪𞤫 𞤅𞤢𞤲𞤼𞤪𞤢𞤤 𞤀𞤼𞤤𞤢𞤧",
            "udm": "𞤓𞤣𞤥𞤵𞤪𞤼𞤪𞤫",
            "ug": "𞤓𞥅𞤴𞤺𞤵𞥅𞤪𞤫",
            "uk": "𞤒𞤵𞤳𞤪𞤫𞤲𞤭𞤴𞤢𞤲𞤪𞤫",
            "umb": "𞤓𞤥𞤦𞤵𞤲𞤣𞤵𞥅𞤪𞤫",
            "und": "𞤍𞤫𞤲𞤺𞤢𞤤 𞤢𞤧-𞤢𞤲𞤣𞤢𞥄𞤲𞤺𞤢𞤤",
            "ur": "𞤓𞤪𞤣𞤵𞥅𞤪𞤫",
            "uz": "𞤓𞥅𞤧𞤦𞤫𞤳𞤪𞤫",
            "vai": "𞤾𞤢𞥄𞤴𞤪𞤫",
            "ve": "𞤏𞤫𞤲𞤣𞤢𞥄𞤪𞤫",
            "vec": "𞤏𞤫𞤲𞤭𞥅𞤧𞤴𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "vi": "𞤏𞤭𞤴𞤫𞤼𞤲𞤢𞤥𞤭𞤲𞤳𞤮𞥅𞤪𞤫",
            "vo": "𞤏𞤮𞤤𞤢𞤨𞤵𞤳𞤪𞤫",
            "vun": "𞤏𞤵𞤲𞤶𞤮𞥅𞤪𞤫",
            "wa": "𞤏𞤢𞥄𞤤𞤮𞤲𞤳𞤮𞥅𞤪𞤫",
            "wae": "𞤏𞤢𞤤𞤧𞤫𞥅𞤪𞤫",
            "wal": "𞤏𞤮𞥅𞤤𞤢𞤴𞤼𞤢𞥄𞤪𞤫",
            "war": "𞤏𞤢𞤪𞤢𞤴𞤫𞥅𞤪𞤫",
            "wo": "𞤏𞤮𞤤𞤮𞤬𞤪𞤫",
            "wuu": "𞤏𞤵𞥅𞤪𞤫 𞤅𞤭𞥅𞤲",
            "xal": "𞤑𞤢𞤤𞤥𞤵𞤳𞤪𞤫",
            "xh": "𞤑𞤮𞥅𞤧𞤢𞥄𞤪𞤫",
            "xog": "𞤅𞤮𞤺𞤢𞥄𞤪𞤫",
            "yav": "𞤒𞤢𞤲𞤺𞤦𞤫𞥅𞤪𞤫",
            "ybb": "𞤒𞤫𞤥𞤦𞤢𞥄𞤪𞤫",
            "yi": "𞤒𞤭𞤣𞤭𞤧𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "yo": "𞤒𞤮𞥅𞤪𞤵𞤦𞤢𞥄𞤪𞤫",
            "yrl": "𞤛𞤫𞥅𞤲𞤺𞤢𞤼𞤵𞥅𞤪𞤫",
            "yue": "𞤑𞤢𞤲𞤼𞤮𞤲𞤫𞥅𞤪𞤫",
            "zgh": "𞤚𞤢𞤥𞤢𞥁𞤭𞤼𞤪𞤫 𞤖𞤢𞤲𞤼𞤵𞤲𞥋𞤣𞤫 𞤃𞤢𞤪𞤮𞥅𞤳",
            "zh": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫",
            "zh-Hans": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤖𞤮𞤴𞤬𞤭𞤲𞤢𞥄𞤲𞤣𞤫",
            "zh-Hant": "𞤕𞤢𞤴𞤲𞤢𞤲𞤳𞤮𞥅𞤪𞤫 𞤒𞤫𞤷𞥆𞤵𞤲𞥋𞤣𞤫",
            "zu": "𞥁𞤵𞤤𞤵𞥅𞤪𞤫",
            "zun": "𞤟𞤵𞤲𞤭𞥅𞤪𞤫",
            "zxx": "𞤀𞤤𞤢𞥄 𞤦𞤢𞤯𞤮𞤪𞤢𞤤 𞤯𞤫𞤲𞤯𞤢𞤲𞤳𞤮",
            "zza": "𞤟𞤢𞥁𞤢𞥄𞤪𞤫"
          },
          "narrow": {
          },
          "short": {
            "az": "𞤀𞤶𞤢𞤪𞤭𞥅𞤪𞤫",
            "en-GB": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤑𞤘)",
            "en-US": "𞤉𞤲𞤺𞤭𞤤𞤫𞥅𞤪𞤫 (𞤁𞤂𞤀)"
          }
        }
      },
      "region": {
        "long": {
          "001": "𞤀𞤣𞤵𞤲𞤢",
          "002": "𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "003": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄",
          "005": "𞤙𞤢𞤥𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄",
          "009": "𞤌𞤧𞤭𞤴𞤢𞤲𞤭𞥅",
          "011": "𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "013": "𞤚𞤵𞤥𞤦𞤮 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄",
          "014": "𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "015": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "017": "𞤚𞤵𞤥𞤦𞤮 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "018": "𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "019": "𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄",
          "021": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄",
          "029": "𞤑𞤢𞤪𞤦𞤭𞤴𞤢𞥄",
          "030": "𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞥄𞤧𞤭𞤴𞤢",
          "034": "𞤙𞤢𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞥄𞤧𞤭𞤴𞤢",
          "035": "𞤙𞤢𞤥𞤬𞤭𞤯𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞥄𞤧𞤭𞤴𞤢",
          "039": "𞤙𞤢𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤓𞤪𞤨𞤢",
          "053": "𞤀𞤧𞤼𞤢𞤪𞤤𞤢𞥄𞤧𞤭𞤴𞤢𞥄",
          "054": "𞤃𞤭𞤤𞤢𞤲𞤭𞥅𞤧𞤴𞤢",
          "057": "𞤖𞤭𞤤𞥆𞤮 𞤃𞤭𞤳𞤪𞤮𞤲𞤭𞥅𞤧𞤸𞤮",
          "061": "𞤆𞤮𞤤𞤭𞤲𞤭𞥅𞤧𞤴𞤢",
          "142": "𞤀𞥄𞤧𞤭𞤴𞤢",
          "143": "𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞥄𞤧𞤭𞤴𞤢",
          "145": "𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤀𞥄𞤧𞤭𞤴𞤢",
          "150": "𞤓𞤪𞤨𞤢",
          "151": "𞤊𞤵𞤯𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤓𞤪𞤨𞤢",
          "154": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤓𞤪𞤨𞤢",
          "155": "𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭 𞤓𞤪𞤨𞤢",
          "202": "𞤀𞤬𞤪𞤭𞤳𞤢𞥄 𞤂𞤫𞤧-𞤅𞤢𞥄𞤸𞤢𞤪𞤢",
          "419": "𞤀𞤥𞤫𞤪𞤭𞤳𞤢𞥄 𞤂𞤢𞤼𞤭𞤲𞤳𞤮",
          "AC": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤀𞤧𞤢𞤲𞤧𞤮𞥅𞤲",
          "AD": "𞤀𞤲𞤣𞤮𞤪𞤢𞥄",
          "AE": "𞤁𞤫𞤲𞤼𞤢𞤤 𞤋𞤥𞤪𞤢𞥄𞤼𞤭 𞤀𞥄𞤪𞤢𞤦𞤵",
          "AF": "𞤀𞤬𞤺𞤢𞤲𞤭𞤧𞤼𞤢𞥄𞤲",
          "AG": "𞤀𞤲𞤼𞤭𞤺𞤵𞤱𞤢 & 𞤄𞤢𞤪𞤦𞤵𞥅𞤣𞤢",
          "AI": "𞤀𞤲𞤺𞤭𞤤𞤢𞥄",
          "AL": "𞤀𞤤𞤦𞤢𞤲𞤭𞤴𞤢𞥄",
          "AM": "𞤀𞤪𞤥𞤫𞤲𞤭𞤴𞤢𞥄",
          "AO": "𞤀𞤲𞤺𞤮𞤤𞤢𞥄",
          "AQ": "𞤀𞤲𞤼𞤢𞤪𞤼𞤭𞤳𞤢𞥄",
          "AR": "𞤀𞤪𞤶𞤢𞤲𞤼𞤭𞤲𞤢𞥄",
          "AS": "𞤅𞤢𞤥𞤵𞤱𞤢 𞤀𞤥𞤫𞤪𞤭𞤳𞤭𞤴𞤢𞤲𞤳𞤮",
          "AT": "𞤌𞤼𞤭𞤪𞤧𞤢",
          "AU": "𞤌𞤧𞤼𞤢𞤪𞤤𞤭𞤴𞤢𞥄",
          "AW": "𞤀𞤪𞤵𞤦𞤢𞥄",
          "AX": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤀𞤤𞤢𞤲𞤣",
          "AZ": "𞤀𞥁𞤫𞤪𞤦𞤢𞤭𞤶𞤢𞥄𞤲",
          "BA": "𞤄𞤮𞤧𞤲𞤭𞤴𞤢 & 𞤖𞤫𞤪𞤧𞤫𞤳𞤮𞤾𞤭𞤲𞤢𞥄",
          "BB": "𞤄𞤢𞤪𞤦𞤫𞥅𞤣𞤮𞥅𞤧",
          "BD": "𞤄𞤢𞤲𞤺𞤭𞤤𞤢𞤣𞤫𞥅𞤧",
          "BE": "𞤄𞤫𞤤𞤶𞤭𞤳𞤢𞥄",
          "BF": "𞤄𞤵𞤪𞤳𞤭𞤲𞤢 𞤊𞤢𞤧𞤮𞥅",
          "BG": "𞤄𞤵𞥅𞤤𞤺𞤢𞤪𞤭𞤴𞤢𞥄",
          "BH": "𞤄𞤢𞤸𞤢𞤪𞤢𞤴𞤲",
          "BI": "𞤄𞤵𞤪𞤵𞤲𞤣𞤭",
          "BJ": "𞤄𞤫𞤲𞤫𞤲",
          "BL": "𞤅𞤼. 𞤄𞤢𞤪𞤼𞤫𞤤𞤭𞤥𞤭",
          "BM": "𞤄𞤭𞤪𞤥𞤵𞤣𞤢",
          "BN": "𞤄𞤵𞤪𞤲𞤢𞥄𞤴",
          "BO": "𞤄𞤮𞤤𞤭𞥅𞤾𞤭𞤴𞤢𞥄",
          "BQ": "𞤑𞤢𞤪𞤦𞤭𞤴𞤢𞥄 𞤖𞤮𞤤𞤢𞤲𞤣𞤭𞤴𞤢𞥄",
          "BR": "𞤄𞤪𞤢𞤧𞤭𞤤",
          "BS": "𞤄𞤢𞤸𞤢𞤥𞤢𞥄𞤧",
          "BT": "𞤄𞤵𞥅𞤼𞤢𞥄𞤲",
          "BV": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤄𞤵𞥅𞤾𞤫𞥅",
          "BW": "‮𞤄𞤮𞤼𞤧𞤵𞤱𞤢𞥄𞤲𞤢",
          "BY": "𞤄𞤫𞤤𞤢𞤪𞤵𞥅𞤧",
          "BZ": "𞤄𞤫𞤤𞤭𞥅𞥁",
          "CA": "𞤑𞤢𞤲𞤢𞤣𞤢𞥄",
          "CC": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤑𞤮𞤳𞤮𞥅𞤧 (𞤑𞤭𞥅𞤤𞤭𞤲𞤺)",
          "CD": "𞤑𞤮𞤲𞤺𞤮 - 𞤑𞤭𞤲𞤧𞤢𞤧𞤢",
          "CF": "𞤐𞤣𞤫𞤲𞤣𞤭 𞤚𞤵𞤥𞤦𞤮𞥅𞤪𞤭 𞤀𞤬𞤪𞤭𞤳𞤢𞥄",
          "CG": "𞤑𞤮𞤲𞤺𞤮 - 𞤄𞤪𞤢𞥁𞤢𞤾𞤭𞤤",
          "CH": "𞤅𞤵𞤱𞤭𞤪𞤧𞤢𞥄",
          "CI": "𞤑𞤮𞤼𞤣𞤭𞤾𞤢𞥄𞤪",
          "CK": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤑𞤵𞥅𞤳",
          "CL": "𞤕𞤭𞤤𞤫𞥊𞥅",
          "CM": "𞤑𞤢𞤥𞤢𞤪𞤵𞥅𞤲",
          "CN": "𞤕𞤢𞤴𞤲𞤢",
          "CO": "𞤑𞤮𞤤𞤮𞤥𞤦𞤭𞤴𞤢𞥄",
          "CP": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤑𞤭𞤤𞤭𞤨𞤫𞤪𞤼𞤮𞤲",
          "CR": "𞤑𞤮𞤧𞤼𞤢 𞤈𞤭𞤳𞤢𞥄",
          "CU": "𞤑𞤵𞥅𞤦𞤢𞥄",
          "CV": "𞤑𞤢𞥄𞤦𞤮 𞤜𞤫𞤪𞤣𞤫",
          "CW": "𞤑𞤵𞥅𞤪𞤢𞤧𞤢𞤱𞤮",
          "CX": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤑𞤭𞤪𞤧𞤭𞤥𞤢𞥄𞤧",
          "CY": "𞤑𞤵𞤦𞤪𞤵𞥅𞤧",
          "CZ": "𞤕𞤫𞥅𞤳𞤭𞤴𞤢𞥄",
          "DE": "𞤔𞤫𞤪𞤥𞤢𞤲𞤭𞥅",
          "DG": "𞤔𞤮𞤺𞤮 𞤘𞤢𞥄𞤪𞤧𞤭𞤴𞤢",
          "DJ": "𞤔𞤭𞤦𞤵𞥅𞤼𞤭",
          "DK": "𞤁𞤢𞤲𞤵𞤥𞤢𞤪𞤳",
          "DM": "𞤁𞤮𞤥𞤭𞤲𞤭𞤳𞤢𞥄",
          "DO": "𞤐𞤣𞤫𞤲𞤣𞤭 𞤁𞤮𞤥𞤭𞤲𞤭𞤳𞤢𞥄",
          "DZ": "𞤀𞤤𞤶𞤢𞤪𞤭𞥅",
          "EA": "𞤅𞤭𞤼𞥆𞤢 & 𞤃𞤫𞤤𞤭𞤤𞤢",
          "EC": "𞤉𞤳𞤵𞤱𞤢𞤣𞤮𞥅𞤪",
          "EE": "𞤉𞤧𞤼𞤮𞤲𞤭𞤴𞤢𞥄",
          "EG": "𞤃𞤭𞤧𞤭𞤪𞤢",
          "EH": "𞤅𞤢𞥄𞤸𞤢𞤪𞤢 𞤖𞤭𞥅𞤲𞤢𞥄𞤪𞤭",
          "ER": "𞤉𞤪𞤭𞥅𞤼𞤫𞤪𞤫",
          "ES": "𞤉𞤧𞤨𞤢𞤻𞤢𞥄",
          "ET": "𞤀𞤦𞤢𞤧𞤭𞤲𞤭𞥅",
          "EU": "𞤑𞤢𞤱𞤼𞤢𞤤 𞤓𞤪𞤨𞤢",
          "EZ": "𞤊𞤭𞤪𞤤𞤢 𞤓𞤪𞤮𞥅",
          "FI": "𞤊𞤭𞤲𞤤𞤢𞤲𞤣",
          "FJ": "𞤊𞤭𞤶𞤭𞥅",
          "FK": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤊𞤢𞤤𞤳𞤵𞤤𞤢𞤲𞤣",
          "FM": "𞤃𞤭𞤳𞤪𞤮𞤲𞤫𞥅𞤧𞤭𞤴𞤢",
          "FO": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤊𞤢𞤪𞤵𞥅𞤧",
          "FR": "𞤊𞤢𞤪𞤢𞤲𞤧𞤭",
          "GA": "𞤘𞤢𞤦𞤮𞤲",
          "GB": "𞤁𞤫𞤲𞤼𞤢𞤤 𞤐𞤺𞤫𞤯𞤵𞥅𞤪𞤭",
          "GD": "𞤘𞤢𞤪𞤲𞤢𞤣𞤢𞥄",
          "GE": "𞤔𞤮𞤪𞤶𞤭𞤴𞤢𞥄",
          "GF": "𞤘𞤵𞤴𞤢𞥄𞤲 𞤊𞤪𞤢𞤲𞤧𞤭𞤲𞤳𞤮",
          "GG": "𞤘𞤢𞤪𞤲𞤫𞤧𞤭𞥅",
          "GH": "𞤘𞤢𞤲𞤢",
          "GI": "𞤔𞤭𞤦𞤪𞤢𞤤𞤼𞤢𞥄",
          "GL": "𞤘𞤭𞤪𞤤𞤢𞤲𞤣𞤭",
          "GM": "𞤘𞤢𞤥𞤦𞤭𞤴𞤢",
          "GN": "𞤘𞤭𞤲𞤫",
          "GP": "𞤘𞤵𞤱𞤢𞤣𞤢𞤤𞤵𞤨𞤫𞥅",
          "GQ": "𞤘𞤭𞤲𞤫 𞤕𞤢𞤳𞤢𞤲𞤼𞤫𞥅𞤪𞤭",
          "GR": "𞤀𞤤𞤴𞤵𞤲𞤢𞥄𞤲",
          "GS": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤔𞤮𞤪𞤶𞤭𞤴𞤢 & 𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫 𞤅𞤢𞤲𞤣𞤵𞤱𞤭𞥅𞤷",
          "GT": "𞤘𞤵𞤱𞤢𞤼𞤫𞤥𞤢𞤤𞤢𞥄",
          "GU": "𞤘𞤵𞤱𞤢𞥄𞤥",
          "GW": "𞤘𞤭𞤲𞤫-𞤄𞤭𞤧𞤢𞤱𞤮𞥅",
          "GY": "𞤘𞤢𞤴𞤢𞤲𞤢𞥄",
          "HK": "𞤖𞤂𞤀 𞤕𞤢𞤴𞤲𞤢 𞤫 𞤖𞤮𞤲𞤺 𞤑𞤮𞤲𞤺",
          "HM": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤖𞤭𞤪𞤣𞤭 & 𞤃𞤢𞤳𞤣𞤮𞤲𞤢𞤤",
          "HN": "𞤖𞤮𞤲𞤣𞤭𞤪𞤢𞥄𞤧",
          "HR": "𞤑𞤵𞤪𞤱𞤢𞥄𞤧𞤭𞤴𞤢",
          "HT": "𞤖𞤢𞤴𞤼𞤭𞥅",
          "HU": "𞤖𞤢𞤲𞤺𞤢𞤪𞤭𞤴𞤢𞥄",
          "IC": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫-𞤑𞤢𞤲𞤢𞤪𞤭𞥅",
          "ID": "𞤋𞤲𞤣𞤮𞤲𞤭𞥅𞤧𞤴𞤢",
          "IE": "𞤋𞤪𞤤𞤢𞤲𞤣",
          "IL": "𞤋𞤧𞤪𞤢𞥄𞤴𞤭𞥅𞤤",
          "IM": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤃𞤫𞥅𞤲",
          "IN": "𞤋𞤲𞤣𞤭𞤴𞤢",
          "IO": "𞤚𞤵𞤥𞤦𞤫𞤪𞤫 𞤄𞤪𞤭𞤼𞤢𞤲𞤭𞤲𞤳𞤮𞥅𞤪𞤫 𞤀𞤬𞤪𞤭𞤳𞤭",
          "IQ": "𞤋𞤪𞤢𞥄𞤳",
          "IR": "𞤋𞤪𞤢𞥄𞤲",
          "IS": "𞤀𞤴𞤧𞤵𞤤𞤢𞤲𞤣",
          "IT": "𞤋𞤼𞤢𞤤𞤭𞥅",
          "JE": "𞤔𞤫𞤪𞤧𞤭𞥅",
          "JM": "𞤔𞤢𞤥𞤢𞤴𞤳𞤢𞥄",
          "JO": "𞤔𞤮𞤪𞤣𞤢𞥄𞤲",
          "JP": "𞤐𞤭𞤨𞥆𞤮𞤲",
          "KE": "𞤑𞤫𞤲𞤭𞤴𞤢𞥄",
          "KG": "𞤑𞤭𞤪𞤶𞤭𞤧𞤼𞤢𞥄𞤲",
          "KH": "𞤑𞤢𞤥𞤦𞤮𞥅𞤣𞤭𞤴𞤢",
          "KI": "𞤑𞤭𞤪𞤦𞤢𞤼𞤭𞥅",
          "KM": "𞤑𞤮𞤥𞤮𞥅𞤪𞤮",
          "KN": "𞤅𞤼. 𞤑𞤪𞤭𞤧𞤼𞤮𞤦𞤢𞤤 & 𞤐𞤫𞥅𞤾𞤭𞤧",
          "KP": "𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞥄 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫",
          "KR": "𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞥄 𞤙𞤢𞤥𞤲𞤢𞥄𞤲𞤺𞤫",
          "KW": "𞤑𞤵𞤱𞤢𞤴𞤼𞤵",
          "KY": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤑𞤢𞤴𞤥𞤢𞥄𞤲",
          "KZ": "𞤑𞤢𞥁𞤢𞤧𞤼𞤢𞥄𞤲",
          "LA": "𞤂𞤢𞤱𞤮𞥅𞤧",
          "LB": "𞤂𞤭𞤦𞤢𞤲𞤮𞥅𞤲",
          "LC": "𞤅𞤼. 𞤂𞤵𞥅𞤧𞤭𞤴𞤢",
          "LI": "𞤂𞤭𞤧𞤼𞤫𞤲𞤧𞤭𞤼𞤫𞥅𞤲",
          "LK": "𞤅𞤭𞤪 𞤂𞤢𞤲𞤳𞤢𞥄",
          "LR": "𞤂𞤢𞤦𞤭𞤪𞤭𞤴𞤢𞥄",
          "LS": "𞤂𞤫𞤧𞤮𞤼𞤮𞥅",
          "LT": "𞤂𞤭𞤼𞤵𞤾𞤢",
          "LU": "𞤂𞤵𞤳𞤧𞤢𞤲𞤦𞤵𞥅𞤺",
          "LV": "𞤂𞤢𞤼𞤾𞤭𞤴𞤢",
          "LY": "𞤂𞤭𞤦𞤭𞤴𞤢𞥄",
          "MA": "𞤃𞤢𞤪𞤮𞥅𞤳",
          "MC": "𞤃𞤮𞤲𞤢𞤳𞤮𞥅",
          "MD": "𞤃𞤮𞤤𞤣𞤮𞤾𞤢𞥄",
          "ME": "𞤃𞤮𞤲𞤼𞤫𞤲𞤫𞥅𞤺𞤮𞤪𞤮",
          "MF": "𞤅𞤼. 𞤃𞤢𞤪𞤼𞤫𞤲",
          "MG": "𞤃𞤢𞤣𞤢𞤺𞤢𞤧𞤳𞤢𞥄𞤪",
          "MH": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤃𞤢𞤪𞥃𞤢𞤤",
          "MK": "𞤃𞤢𞤳𞤫𞤣𞤮𞤲𞤭𞤴𞤢𞥄 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫",
          "ML": "𞤃𞤢𞥄𞤤𞤭",
          "MM": "𞤃𞤭𞤴𞤢𞤥𞤢𞥄𞤪 (𞤄𞤵𞥅𞤪𞤥𞤢)",
          "MN": "𞤃𞤮𞤲𞤺𞤮𞤤𞤭𞤴𞤢",
          "MO": "𞤖𞤂𞤀 𞤕𞤢𞤴𞤲𞤢 𞤫 𞤃𞤢𞤳𞤢𞤱𞤮𞥅",
          "MP": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤃𞤢𞤪𞤭𞤴𞤢𞥄𞤲𞤢 𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞥅𞤪𞤭",
          "MQ": "𞤃𞤢𞤪𞤼𞤭𞤲𞤭𞤳𞤢𞥄",
          "MR": "𞤃𞤮𞤪𞤼𞤢𞤲𞤭𞥅",
          "MS": "𞤃𞤮𞤲𞤧𞤭𞤪𞤢𞥄𞤼",
          "MT": "𞤃𞤢𞤤𞤼𞤢",
          "MU": "𞤃𞤮𞤪𞤭𞥅𞤧𞤭",
          "MV": "𞤃𞤢𞤤𞤣𞤭𞥅𞤬",
          "MW": "𞤃𞤢𞤤𞤢𞤱𞤭𞥅",
          "MX": "𞤃𞤫𞤳𞤧𞤭𞤳𞤮𞥅",
          "MY": "𞤃𞤢𞤤𞤫𞥅𞤧𞤭𞤴𞤢",
          "MZ": "𞤃𞤮𞤧𞤢𞤥𞤦𞤭𞥅𞤳",
          "NA": "𞤐𞤢𞤥𞤭𞥅𞤦𞤭𞤴𞤢𞥄",
          "NC": "𞤑𞤢𞤤𞤭𞤣𞤮𞤲𞤭𞤴𞤢𞥄 𞤖𞤫𞤧𞤮",
          "NE": "𞤐𞤭𞥅𞤶𞤫𞤪",
          "NF": "𞤅𞤵𞤪𞤭𞥅𞤪𞤫 𞤐𞤮𞤪𞤬𞤮𞤤𞤳𞤵",
          "NG": "𞤐𞤢𞤶𞤫𞤪𞤭𞤴𞤢𞥄",
          "NI": "𞤐𞤭𞤳𞤢𞤪𞤢𞤺𞤵𞤱𞤢𞥄",
          "NL": "𞤖𞤮𞤤𞤢𞤲𞤣𞤭𞤴𞤢𞥄",
          "NO": "𞤐𞤮𞤪𞤺𞤫𞤴𞤢𞥄",
          "NP": "𞤐𞤭𞤨𞤢𞥄𞤤",
          "NR": "𞤐𞤢𞤱𞤪𞤵",
          "NU": "𞤐𞤵𞥅𞤱𞤭",
          "NZ": "𞤐𞤫𞤱 𞤟𞤫𞤤𞤢𞤲𞤣",
          "OM": "𞤌𞥅𞤥𞤢𞥄𞤲",
          "PA": "𞤆𞤢𞤲𞤢𞤥𞤢",
          "PE": "𞤆𞤫𞤪𞤵𞥅",
          "PF": "𞤆𞤮𞤤𞤭𞤲𞤫𞥅𞤧𞤭𞤴𞤢 𞤊𞤪𞤢𞤲𞤧𞤭𞤲𞤳𞤮",
          "PG": "𞤆𞤢𞤨𞤵𞤱𞤢 𞤘𞤭𞤲𞤫 𞤖𞤫𞤧𞤮",
          "PH": "𞤊𞤭𞤤𞤭𞤨𞤭𞥅𞤲",
          "PK": "𞤆𞤢𞤳𞤭𞤧𞤼𞤢𞥄𞤲",
          "PL": "𞤆𞤮𞤤𞤢𞤲𞤣",
          "PM": "𞤅𞤼. 𞤆𞤭𞤴𞤫𞥅𞤪 & 𞤃𞤭𞤳𞤫𞤤𞤮𞤲",
          "PN": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤆𞤭𞤼𞤳𞤭𞥅𞤪𞤲𞤵",
          "PR": "𞤆𞤮𞤪𞤼𞤮 𞤈𞤭𞤳𞤮𞥅",
          "PS": "𞤂𞤫𞤧𞤣𞤭𞥅𞤶𞤭 𞤊𞤢𞤤𞤫𞤧𞤼𞤭𞥅𞤲",
          "PT": "𞤆𞤮𞥅𞤪𞤼𞤵𞤺𞤢𞥄𞤤",
          "PW": "𞤆𞤢𞤤𞤢𞤱",
          "PY": "𞤆𞤢𞥄𞤪𞤢𞤺𞤵𞤱𞤢𞥄𞤴",
          "QA": "𞤊𞤢𞤤𞤫𞤧𞤼𞤭𞥅𞤲",
          "QO": "𞤚𞤢𞤼𞥆𞤫𞥅𞤪𞤭 𞤌𞤧𞤴𞤢𞤲𞤭𞤴𞤢",
          "RE": "𞤈𞤫𞥅𞤲𞤭𞤴𞤮𞤲",
          "RO": "𞤈𞤵𞤥𞤢𞥄𞤲𞤭𞤴𞤢",
          "RS": "𞤅𞤫𞤪𞤦𞤭𞤴𞤢𞥄",
          "RU": "𞤈𞤮𞥅𞤧𞤭𞤴𞤢",
          "RW": "𞤈𞤵𞤱𞤢𞤲𞤣𞤢𞥄",
          "SA": "𞤅𞤢𞤵𞥅𞤣 𞤀𞥄𞤪𞤢𞤦𞤭𞤴𞤢𞥄",
          "SB": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤅𞤵𞤤𞤢𞤴𞤥𞤢𞥄𞤲",
          "SC": "𞤅𞤫𞤴𞤭𞤧𞤫𞤤",
          "SD": "𞤅𞤵𞤣𞤢𞥄𞤲",
          "SE": "𞤅𞤵𞤱𞤫𞤣𞤭𞤴𞤢𞥄",
          "SG": "𞤅𞤭𞤲𞤺𞤢𞤨𞤵𞥅𞤪",
          "SH": "𞤅𞤫𞤲-𞤖𞤫𞤤𞤫𞤲𞤢𞥄",
          "SI": "𞤅𞤵𞤤𞤮𞤾𞤫𞤲𞤭𞤴𞤢𞥄",
          "SJ": "𞤅𞤢𞤾𞤢𞤤𞤦𞤢𞤪𞤣 & 𞤔𞤢𞤲 𞤃𞤢𞤴𞤫𞤲",
          "SK": "𞤅𞤵𞤤𞤮𞤾𞤢𞥄𞤳𞤭𞤴𞤢",
          "SL": "𞤅𞤢𞤪𞤢𞤤𞤮𞤲",
          "SM": "𞤅𞤢𞤲 𞤃𞤢𞤪𞤭𞤲𞤮𞥅",
          "SN": "𞤅𞤫𞤲𞤫𞤺𞤢𞥄𞤤",
          "SO": "𞤅𞤵𞥅𞤥𞤢𞥄𞤤𞤭",
          "SR": "𞤅𞤵𞤪𞤭𞤲𞤢𞥄𞤥",
          "SS": "𞤅𞤵𞤣𞤢𞥄𞤲 𞤂𞤫𞤧𞤤𞤫𞤴𞤪𞤭",
          "ST": "𞤅𞤢𞤱𞤵 𞤚𞤵𞤥𞤫𞥅 & 𞤆𞤫𞤪𞤫𞤲𞤧𞤭𞤨𞤫",
          "SV": "𞤉𞤤 𞤅𞤢𞤤𞤾𞤢𞤣𞤮𞥅𞤪",
          "SX": "𞤅𞤫𞤲𞤼𞤵 𞤃𞤢𞥄𞤪𞤼𞤫𞤲",
          "SY": "𞤅𞤵𞥅𞤪𞤭𞤴𞤢𞥄",
          "SZ": "𞤉𞤧𞤱𞤢𞤼𞤭𞤲𞤭",
          "TA": "𞤚𞤵𞤪𞤧𞤵𞤼𞤢𞤲 𞤁𞤢𞤳𞤵𞤲𞤸𞤢",
          "TC": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤚𞤵𞤪𞤳𞤵𞤧 & 𞤑𞤢𞤴𞤳𞤮𞥅𞤧",
          "TD": "𞤕𞤢𞥄𞤣",
          "TF": "𞤚𞤵𞤥𞤦𞤫 𞤂𞤫𞤧𞤤𞤫𞤴𞤶𞤫 𞤊𞤪𞤢𞤲𞤧𞤭",
          "TG": "𞤚𞤮𞤺𞤮",
          "TH": "𞤚𞤢𞥄𞤴𞤤𞤢𞤲𞤣",
          "TJ": "𞤚𞤢𞤶𞤭𞤳𞤭𞤧𞤼𞤢𞥄𞤲",
          "TK": "𞤚𞤮𞥅𞤳𞤮𞤤𞤢𞥄𞤱𞤵",
          "TL": "𞤚𞤭𞤥𞤮𞥅𞤪 𞤂𞤫𞤧𞤼𞤫",
          "TM": "𞤚𞤵𞤪𞤳𞤵𞤥𞤫𞤲𞤭𞤧𞤼𞤢𞥄𞤲",
          "TN": "𞤚𞤵𞤲𞤭𞥅𞤧𞤢",
          "TO": "𞤚𞤮𞤲𞤺𞤢",
          "TR": "𞤚𞤵𞤪𞤳𞤭𞤴𞤢𞥄",
          "TT": "𞤚𞤭𞤪𞤲𞤭𞤣𞤢𞥄𞤣 & 𞤚𞤮𞤦𞤢𞤺𞤮𞥅",
          "TV": "𞤚𞤵𞥅𞤾𞤢𞤤𞤵",
          "TW": "𞤚𞤢𞤴𞤱𞤢𞥄𞤲",
          "TZ": "𞤚𞤢𞤲𞤧𞤢𞤲𞤭𞥅",
          "UA": "𞤓𞤳𞤪𞤫𞥅𞤲𞤭𞤴𞤢",
          "UG": "𞤓𞤺𞤢𞤲𞤣𞤢𞥄",
          "UM": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤁𞤢𞥄𞤴𞤭𞥅𞤯𞤫 𞤁𞤂𞤀",
          "UN": "𞤑𞤢𞤱𞤼𞤢𞤤 𞤘𞤫𞤲𞤯𞤭",
          "US": "𞤁𞤫𞤲𞤼𞤢𞤤 𞤂𞤢𞤪𞤫",
          "UY": "𞤒𞤵𞤪𞤺𞤮𞤴",
          "UZ": "𞤓𞥁𞤦𞤫𞤳𞤭𞤧𞤼𞤢𞥄𞤲",
          "VA": "𞤜𞤢𞤼𞤭𞤳𞤢𞥄𞤲",
          "VC": "𞤅𞤼. 𞤜𞤭𞤲𞤧𞤢𞤲 & 𞤘𞤭𞤪𞤲𞤢𞤣𞤭𞥅𞤲",
          "VE": "𞤜𞤫𞥊𞤲𞤭𞥅𞥁𞤵𞤱𞤫𞤤𞤢𞥄",
          "VG": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤜𞤭𞤪𞤺𞤭𞥅𞤲 𞤄𞤪𞤭𞤼𞤢𞤲𞤭𞤴𞤢𞤲𞤳𞤮𞥅𞤶𞤫",
          "VI": "𞤕𞤵𞤪𞤭𞥅𞤶𞤫 𞤜𞤭𞤪𞤺𞤭𞥅𞤲 𞤁𞤂𞤀",
          "VN": "𞤜𞤭𞤴𞤫𞤼𞤲𞤢𞥄𞤥",
          "VU": "𞤜𞤢𞤲𞤵𞤱𞤢𞥄𞤼𞤵",
          "WF": "𞤏𞤢𞤤𞥆𞤭𞥅𞤧 & 𞤊𞤵𞤼𞤵𞤲𞤢",
          "WS": "𞤅𞤢𞤥𞤵𞤱𞤢",
          "XA": "𞤌𞤻𞤵𞤲𞤣𞤫-𞤃𞤢𞤧𞤫",
          "XB": "𞤌𞤻𞤵𞤲𞤣𞤫-𞤄𞤭𞤣𞤭",
          "XK": "𞤑𞤮𞥅𞤧𞤮𞤾𞤮𞥅",
          "YE": "𞤒𞤢𞤥𞤢𞤲",
          "YT": "𞤃𞤢𞤴𞤮𞥅𞤼𞤵",
          "ZA": "𞤀𞤬𞤪𞤭𞤳𞤢 𞤂𞤫𞤧𞤤𞤫𞤴𞤪𞤭",
          "ZM": "𞤟𞤢𞤥𞤦𞤭𞤴𞤢",
          "ZW": "𞤟𞤭𞤥𞤦𞤢𞥄𞤥𞤵𞤴𞤢",
          "ZZ": "𞤖𞤭𞤤𞥆𞤮 𞤀𞤧-𞤢𞤲𞤣𞤢𞥄𞤲𞤺𞤮"
        },
        "narrow": {
        },
        "short": {
          "GB": "𞤑𞤘",
          "HK": "𞤖𞤮𞤲𞤺 𞤑𞤮𞤲𞤺",
          "MO": "𞤃𞤢𞤳𞤢𞤱𞤮𞥅",
          "PS": "𞤂𞤫𞤧𞤣𞤭𞥅𞤶𞤭 𞤊𞤢𞤤𞤫𞤧𞤼𞤭𞥅𞤲",
          "US": "𞤁𞤂𞤀"
        }
      },
      "script": {
        "long": {
          "Adlm": "𞤀𞤁𞤂𞤢𞤃",
          "Aghb": "𞤀𞤹𞤦𞤢𞤲𞤭𞤴𞤢",
          "Ahom": "𞤀𞤸𞤮𞤥",
          "Arab": "𞤀𞥄𞤪𞤢𞤦𞤵",
          "Aran": "𞤐𞤢𞤧𞤼𞤢𞤤𞤭𞤹",
          "Armi": "𞤀𞤪𞤢𞤥𞤭𞤴𞤢 𞤉𞤥𞤨𞤫𞤪𞤭𞤴𞤢𞤤",
          "Armn": "𞤀𞤪𞤥𞤫𞤲𞤭𞤴𞤢𞤲",
          "Avst": "𞤀𞤾𞤫𞤧𞤼𞤢𞤲",
          "Bali": "𞤄𞤢𞤤𞤭𞤲𞤭𞥅𞤧",
          "Bamu": "𞤄𞤢𞤥𞤵",
          "Bass": "𞤄𞤢𞤧𞤢𞥄 𞤜𞤢𞥄",
          "Batk": "𞤄𞤢𞤼𞤢𞤳",
          "Beng": "𞤄𞤫𞤲𞤺𞤢𞤤𞤭",
          "Bhks": "𞤄𞤢𞤴𞤳𞤵𞤧𞤵𞤳𞤭",
          "Bopo": "𞤄𞤮𞤨𞤮𞤥𞤮𞤬𞤮",
          "Brah": "𞤄𞤪𞤢𞤸𞤢𞤥𞤭",
          "Brai": "𞤄𞤢𞤪𞤢𞤭𞥅𞤤𞤵",
          "Bugi": "𞤄𞤵𞤺𞤭𞤲𞤭𞤴𞤢",
          "Buhd": "𞤄𞤵𞤸𞤭𞤣",
          "Cakm": "𞤕𞤢𞤳𞤥𞤢",
          "Cans": "𞤑𞤢𞤱𞤪𞤢𞤤 𞤅𞤭𞤺𞤢𞤲𞤯𞤫 𞤚𞤢𞥄𞤳𞤢𞤲𞤶𞤫 𞤑𞤢𞤲𞤢𞤣𞤢𞥄",
          "Cari": "𞤑𞤢𞤪𞤭𞤴𞤢𞤲",
          "Cham": "𞤕𞤢𞥄𞤥",
          "Cher": "𞤕𞤫𞤪𞤮𞤳𞤭𞥅",
          "Chrs": "𞤑𞤮𞤪𞤢𞥄𞤧𞤥𞤭𞤴𞤢",
          "Copt": "𞤑𞤭𞤦𞤯𞤭𞤲𞤳𞤮",
          "Cpmn": "𞤅𞤭𞥅𞤨𞤪𞤮 𞤃𞤭𞤲𞤮𞤴𞤢",
          "Cprt": "𞤑𞤵𞤦𞤭𞤪𞤧𞤵",
          "Cyrl": "𞤅𞤭𞤪𞤤𞤭𞤳",
          "Deva": "𞤁𞤫𞤾𞤢𞤲𞤢𞤺𞤢𞤪𞤭",
          "Diak": "𞤁𞤭𞤾𞤫𞥅𞤧 𞤀𞤳𞤵𞤪𞤵",
          "Dogr": "𞤁𞤮𞤺𞤪𞤢",
          "Dsrt": "𞤁𞤫𞤧𞤫𞤪𞤫𞤼",
          "Dupl": "𞤁𞤵𞤨𞤤𞤮𞤴𞤢𞤲 𞤅𞤮𞥅𞤪𞤼𞤤𞤢𞤲𞤣",
          "Egyp": "𞤖𞤭𞤪𞤮𞤺𞤭𞤪𞤬𞤵 𞤃𞤭𞤧𞤭𞤪𞤢",
          "Elba": "𞤉𞤤𞤦𞤢𞤧𞤢𞤲",
          "Elym": "𞤉𞤤𞤴𞤥𞤢𞤴𞤳",
          "Ethi": "𞤖𞤢𞤦𞤢𞤧𞤭𞤲𞤳𞤮",
          "Geor": "𞤔𞤮𞤪𞤶𞤭𞤴𞤢𞤲",
          "Glag": "𞤘𞤭𞤤𞤢𞤺𞤮𞤤𞤭𞤼𞤭𞤳",
          "Gong": "𞤘𞤵𞤲𞤶𞤢𞤤𞤢 𞤘𞤮𞤲𞤣𞤭",
          "Gonm": "𞤃𞤢𞤧𞤢𞤪𞤢𞤲 𞤘𞤮𞤲𞤣𞤭",
          "Goth": "𞤘𞤵𞥅𞤼𞤭𞤲𞤳𞤮",
          "Gran": "𞤘𞤢𞤪𞤢𞤲𞤼𞤢",
          "Grek": "𞤘𞤭𞤪𞤧𞤢",
          "Gujr": "𞤘𞤵𞤶𞤢𞤪𞤢𞤼𞤭𞥅",
          "Guru": "𞤘𞤵𞤪𞤥𞤵𞤿𞤭",
          "Hanb": "𞤖𞤢𞥄𞤲 𞤫 𞤄𞤮𞤨𞤮𞤥𞤮𞤬𞤮",
          "Hang": "𞤖𞤢𞤲𞤺𞤵𞥅𞤤",
          "Hani": "𞤖𞤢𞥄𞤲",
          "Hano": "𞤖𞤢𞤲𞤵𞥅𞤲𞤮",
          "Hans": "𞤖𞤮𞤴𞤲𞤢𞥄𞤲𞤣𞤫",
          "Hant": "𞤚𞤢𞤱𞤢𞥄𞤲𞤣𞤫",
          "Hatr": "𞤖𞤢𞤼𞤢𞤪𞤢𞤲",
          "Hebr": "𞤖𞤢𞤦𞤵𞤪𞤢",
          "Hira": "𞤖𞤭𞤪𞤢𞤺𞤢𞤲𞤢",
          "Hluw": "𞤖𞤭𞤪𞤮𞤺𞤭𞤪𞤬𞤵 𞤀𞤲𞤢𞤼𞤮𞤤𞤭𞤴𞤢",
          "Hmng": "𞤆𞤢𞤸𞤢𞤱 𞤖𞤢𞤥𞤮𞤲𞤺",
          "Hmnp": "𞤙𞤭𞤢𞤳𞤫𞤲𞤺 𞤆𞤵𞤢𞤧𞤵𞥅 𞤖𞤥𞤮𞤲𞤺",
          "Hrkt": "𞤅𞤭𞤺𞤢𞤲𞤯𞤫 𞤐𞤭𞤨𞤮𞤲𞤶𞤫",
          "Hung": "𞤑𞤭𞤯𞥆𞤭 𞤖𞤢𞤲𞤺𞤢𞤪𞤭𞤴𞤢𞥄",
          "Ital": "𞤑𞤭𞤯𞤭 𞤋𞤼𞤢𞤤𞤭𞤳",
          "Jamo": "𞤔𞤢𞤥𞤮",
          "Java": "𞤟𞤢𞤾𞤢𞥄",
          "Jpan": "𞤐𞤭𞤨𞤮𞤲",
          "Kali": "𞤑𞤢𞤴𞤢 𞤂𞤭",
          "Kana": "𞤑𞤢𞤼𞤢𞤳𞤢𞤲𞤢",
          "Kawi": "𞤑𞤢𞤱𞤭",
          "Khar": "𞤑𞤢𞤪𞤮𞥃𞤢𞤼𞤭",
          "Khmr": "𞤑𞤵𞤥𞤫𞥅𞤪",
          "Khoj": "𞤑𞤮𞤶𞤳𞤭",
          "Kits": "𞤄𞤭𞤲𞤳𞤮𞤴 𞤑𞤭𞤼𞤢𞤲",
          "Knda": "𞤑𞤢𞤲𞥆𞤢𞤣𞤢",
          "Kore": "𞤑𞤮𞥅𞤪𞤫𞤴𞤢𞤲",
          "Kthi": "𞤑𞤢𞤭𞤼𞤭",
          "Lana": "𞤂𞤢𞤲𞥆𞤢",
          "Laoo": "𞤂𞤢𞤱𞤮𞥅",
          "Latn": "𞤂𞤢𞤼𞤫𞤲",
          "Lepc": "𞤂𞤫𞤨𞤷𞤢",
          "Limb": "𞤂𞤭𞤥𞤦𞤵",
          "Lina": "𞤊𞤮𞤷𞥆𞤭𞥅𞤲𞤺𞤮𞤤 𞤀",
          "Linb": "𞤊𞤮𞤷𞥆𞤭𞥅𞤲𞤺𞤮𞤤 𞤄",
          "Lisu": "𞤂𞤭𞤧𞤵",
          "Lyci": "𞤂𞤭𞥅𞤧𞤭𞤴𞤢𞤲",
          "Lydi": "𞤂𞤭𞤣𞤭𞤴𞤢𞤲",
          "Mahj": "𞤃𞤢𞤸𞤢𞤶𞤢𞤲𞤭𞥅",
          "Maka": "𞤃𞤢𞤳𞤢𞤧𞤢𞤪",
          "Mand": "𞤃𞤢𞤲𞤣𞤫𞥅𞤲",
          "Mani": "𞤃𞤢𞤲𞤭𞤳𞤭𞤴𞤫𞤲",
          "Marc": "𞤃𞤢𞤪𞤷𞤫𞤲",
          "Medf": "𞤃𞤢𞤣𞤬𞤫𞤣𞤭𞤪𞤭𞥅𞤲",
          "Mend": "𞤃𞤫𞤲𞤣𞤫",
          "Merc": "𞤃𞤫𞤪𞤱𞤫𞤼𞤭𞤳 𞤅𞤢𞤪𞤰𞤵𞤯𞤭",
          "Mero": "𞤃𞤫𞤪𞤱𞤫𞤼𞤭𞤳",
          "Mlym": "𞤃𞤢𞤤𞤢𞤴𞤢𞤤𞤢𞤥",
          "Modi": "𞤃𞤮𞤣𞤭",
          "Mong": "𞤃𞤮𞤲𞤺𞤮𞤤𞤭𞤴𞤢𞤲",
          "Mroo": "𞤃𞤮𞤪𞤮𞥅",
          "Mtei": "𞤃𞤫𞤼𞤭 𞤃𞤢𞤴𞤫𞤳",
          "Mult": "𞤃𞤵𞤤𞤼𞤢𞤲𞤭",
          "Mymr": "𞤃𞤭𞤴𞤢𞤥𞤢𞥄𞤪",
          "Nagm": "𞤐𞤢𞤺 𞤃𞤵𞤲𞤣𞤢𞤪𞤭",
          "Nand": "𞤐𞤢𞤲𞤣𞤭𞤲𞤢𞤺𞤢𞤪𞤭",
          "Narb": "𞤐𞤢𞤲𞥆𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮 𞤀𞥄𞤪𞤢𞤦𞤵 𞤑𞤭𞤯𞤭",
          "Nbat": "𞤐𞤢𞤦𞤢𞤼𞤭𞤴𞤢𞤲",
          "Newa": "𞤐𞤭𞤱𞤢",
          "Nkoo": "𞤐𞤳𞤮𞥅",
          "Nshu": "𞤐𞤵𞥅𞤧𞤵",
          "Ogam": "𞤌𞥅𞤺𞤢𞤥𞤵",
          "Olck": "𞤌𞤭-𞤕𞤭𞤳𞤭",
          "Orkh": "𞤌𞤪𞤳𞤮𞥅𞤲",
          "Orya": "𞤌𞤪𞤭𞤴𞤢",
          "Osge": "𞤌𞤧𞤢𞥄𞤶",
          "Osma": "𞤌𞤧𞤥𞤢𞤲𞤭𞤴𞤢",
          "Ougr": "𞤏𞤭𞤺𞤵𞥅𞤪 𞤑𞤭𞤯𞥆𞤵𞤲",
          "Palm": "𞤆𞤢𞤤𞤥𞤫𞤪𞤫𞥅𞤲",
          "Pauc": "𞤆𞤢𞤱 𞤅𞤭𞥅𞤲 𞤖𞤢𞥄𞤱",
          "Perm": "𞤆𞤫𞤪𞤥𞤭𞤳 𞤑𞤭𞤯𞥆𞤵𞤲",
          "Phag": "𞤊𞤢𞤺𞤧 𞤆𞤢",
          "Phli": "𞤄𞤭𞤲𞤣𞤭 𞤆𞤢𞤤𞤢𞤾𞤭",
          "Phlp": "𞤅𞤮𞤤𞤼𞤮𞥅 𞤆𞤢𞤤𞤢𞤾𞤭",
          "Phnx": "𞤊𞤭𞤲𞤭𞤳𞤭𞤴𞤢𞤲𞤳𞤮",
          "Plrd": "𞤖𞤭𞤼𞤮𞤲𞤳𞤮 𞤆𞤮𞤤𞥆𞤢𞤪𞤣",
          "Prti": "𞤄𞤭𞤲𞤣𞤭 𞤆𞤢𞤪𞤧𞤭𞤴𞤢𞤲",
          "Qaag": "𞤟𞤢𞤱𞤺𞤭𞥅𞤴𞤵",
          "Rjng": "𞤈𞤭𞤶𞤢𞤲𞤺",
          "Rohg": "𞤖𞤢𞤲𞤭𞤬𞤭",
          "Runr": "𞤈𞤵𞤲𞤭𞥅𞤳",
          "Samr": "𞤅𞤢𞤥𞤢𞤪𞤭𞤼𞤢𞤲",
          "Sarb": "𞤙𞤢𞥄𞤥𞤲𞤢𞥄𞤲𞤺𞤫𞤲𞤳𞤮 𞤀𞥄𞤪𞤢𞤦𞤵 𞤑𞤭𞤯𞤭",
          "Saur": "𞤅𞤢𞤵𞤪𞤢𞥃𞤼𞤪𞤢",
          "Sgnw": "𞤄𞤭𞤲𞤣𞤭 𞤊𞤭𞤲𞤣𞤫",
          "Shaw": "𞤅𞤢𞤬𞤭𞤴𞤢𞥄𞤲",
          "Shrd": "𞤡𞤢𞤪𞤢𞤣𞤢",
          "Sidd": "𞤅𞤭𞤣𞥆𞤢𞥄𞤥",
          "Sind": "𞤑𞤵𞤣𞤢𞤱𞤢𞤣𞤭",
          "Sinh": "𞤅𞤭𞤲𞤸𞤢𞤤𞤢",
          "Sogd": "𞤅𞤮𞤺𞤮𞤣𞤭𞤴𞤢𞤲",
          "Sogo": "𞤅𞤮𞤺𞤮𞤣𞤭𞤴𞤢𞤲 𞤑𞤭𞤯𞥆𞤵𞤲",
          "Sora": "𞤅𞤢𞤪𞤢 𞤅𞤮𞤥𞤨𞤢𞤲𞤺",
          "Soyo": "𞤅𞤮𞤴𞤮𞤥𞤦𞤮",
          "Sund": "𞤅𞤵𞤲𞤣𞤢𞤲𞤭",
          "Sylo": "𞤅𞤴𞤤𞤮𞤼𞤭𞥅 𞤐𞤢𞤺𞤪𞤭",
          "Syrc": "𞤅𞤭𞥅𞤪𞤴𞤢𞤳",
          "Tagb": "𞤚𞤢𞤺𞤦𞤢𞤲𞤱𞤢",
          "Takr": "𞤚𞤢𞤳𞤪𞤭",
          "Tale": "𞤚𞤢𞥄𞤴 𞤂𞤭𞥅",
          "Talu": "𞤚𞤢𞥄𞤴 𞤂𞤵𞤫 𞤑𞤫𞤧𞤮",
          "Taml": "𞤚𞤢𞤥𞤭𞤤",
          "Tang": "𞤚𞤢𞤲𞤺𞤵𞤼",
          "Tavt": "𞤚𞤢𞥄𞤴 𞤜𞤭𞤫𞥅𞤼",
          "Telu": "𞤚𞤫𞤤𞤵𞤺𞤵",
          "Tfng": "𞤚𞤭𞤬𞤭𞤲𞤢𞥄𞤺",
          "Tglg": "𞤚𞤢𞤺𞤢𞤤𞤮𞤺",
          "Thaa": "𞤡𞤢𞥄𞤲𞤢",
          "Thai": "𞤚𞤢𞤱𞤤𞤢𞤲𞤣",
          "Tibt": "𞤚𞤭𞤦𞤫𞤼𞤢𞤲",
          "Tirh": "𞤚𞤭𞤪𞤸𞤵𞤼𞤢",
          "Tnsa": "𞤚𞤢𞤲𞤺𞤧𞤢",
          "Toto": "𞤚𞤮𞤼𞤮",
          "Ugar": "𞤓𞤺𞤢𞤪𞤭𞤼𞤭𞤳",
          "Vaii": "𞤜𞤢𞥄𞤴",
          "Vith": "𞤜𞤭𞤼𞤳𞤵𞤹𞤭",
          "Wara": "𞤜𞤢𞤪𞤢𞤲𞤺 𞤑𞤭𞥃𞤼𞤭",
          "Wcho": "𞤏𞤢𞤲𞤷𞤮𞥅",
          "Xpeo": "𞤊𞤢𞥄𞤪𞤭𞤧𞤭𞤴𞤢𞤲𞤳𞤮 𞤑𞤭𞤯𞥆𞤵𞤲",
          "Xsux": "𞤅𞤵𞤥𞤫𞤪𞤮 𞤀𞤳𞥆𞤢𞤣𞤭𞤴𞤢𞤲 𞤑𞤵𞤲𞤫𞤬𞤮𞤪𞤥",
          "Yezi": "𞤒𞤢𞤶𞤭𞤣𞤭𞥅𞤴𞤵",
          "Yiii": "𞤒𞤭𞥅",
          "Zanb": "𞤟𞤢𞤲𞤢𞤦𞤢𞥁𞤢𞥄𞤪 𞤁𞤭𞤲𞤺𞤫𞤪𞤫",
          "Zinh": "𞤁𞤮𞤲𞤣𞤭",
          "Zmth": "𞤍𞤵𞤪𞥆𞤢𞥄𞤲𞤺𞤮 𞤂𞤭𞤥𞤭𞤲𞤳𞤮",
          "Zsye": "𞤐𞤺𞤮𞤼𞥆𞤭",
          "Zsym": "𞤋𞤥𞥆𞤮𞤪𞤫",
          "Zxxx": "𞤀𞤧𞤱𞤭𞤲𞤣𞤢𞥄𞤯𞤵𞤲",
          "Zyyy": "𞤑𞤢𞤬𞤵",
          "Zzzz": "𞤄𞤭𞤲𞤣𞤭 𞤀𞤧-𞤢𞤲𞤣𞤢𞥄𞤯𞤭"
        },
        "narrow": {
        },
        "short": {
        }
      }
    }
  },
  "locale": "ff-Adlm-BF"
})
}